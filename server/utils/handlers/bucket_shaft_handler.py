import logging
import re
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Any
from config_file import config
from server.utils.handlers.base_handler import BaseFrameHandler


class BucketShaftHandler(BaseFrameHandler):
    """耙斗井处理器类
    
    负责处理耙斗井相关的视频帧
    """
    
    def __init__(self, processor):
        """初始化耙斗井处理器
        
        Args:
            processor (VideoFrameProcessor): 主处理器实例
        """
        super().__init__(processor)
        
        # 初始化耙斗井YOLO检测器
        from llms.yolo_api_server_dipper_shaft import DipperShaftDetector
        
        # 从config_file获取配置信息，提供默认值
        model_path = 'llms/models/yolo/best-dipper-shaft.pt'  # 默认模型路径
        self.save_bucket_shaft_result = False  # 默认不保存结果
        
        # 从全局配置获取模型路径和保存设置
        if config.env:
            # 优先查找 yolo_model_bucket_shaft，如果没有则查找 yolo_model_dipper_shaft
            model_path = config.env.get('yolo_model_bucket_shaft', 
                                       config.env.get('yolo_model_dipper_shaft', model_path))
            
            # 获取YOLO保存结果配置
            yolo_save_config = config.env.get('yolo_save_result', {})
            # 优先查找 bucket_shaft，如果没有则查找 dipper_shaft
            self.save_bucket_shaft_result = yolo_save_config.get('bucket_shaft', 
                                                               yolo_save_config.get('dipper_shaft', False))
            logging.info(f"从配置文件获取耙斗井检测器设置：模型路径={model_path}, 保存结果={self.save_bucket_shaft_result}")
        else:
            logging.warning("无法获取配置信息，使用默认设置")
        
        self.bucket_shaft_detector = DipperShaftDetector(model_path=model_path)
        logging.info(f"耙斗井检测器初始化完成，模型路径: {model_path}, 保存结果: {self.save_bucket_shaft_result}")
    
    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理耙斗井相关的视频帧
        
        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"耙斗井处理器开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}")
        
        # frame_filename = f"frame_{camera_id}_{current_time.strftime('%Y_%m_%d_%H_%M_%S')}.jpg"
        # frame_path = save_dir / frame_filename
        resized_frame, frame_path = self._prepare_frame(frame, save_dir, camera_id, current_time)
        failure_reasons_type = []  # 出现故障的类型
        
        # 调整图像尺寸为1600x900
        # resized_frame = cv2.resize(frame, (1600, 900), interpolation=cv2.INTER_AREA)
        
        # 保存调整后的帧
        # cv2.imwrite(str(frame_path), resized_frame)
        
        # 根据不同类型的耙斗井系统执行不同的处理逻辑
        """
        # 原有的大模型处理方式（已注释，保留备用）
        if system_type == 'system_prompt_bucket_shaft':
            # 处理单图耙斗井
            system_prompts = ['system_prompt_waste_percentage', 'system_prompt_bulky_waste']
            response_dict = {}
            for prompt in system_prompts:
                # 默认不保存中间处理图像
                response = self._process_image_comparison(resized_frame, frame_path, standard_image_path, prompt)
                response_dict[prompt] = response
        elif system_type == 'system_prompt_bucket_shaft_multiple':
            # 处理多图耙斗井
            system_prompts = ['system_prompt_waste_percentage_multiple', 'system_prompt_bulky_waste_multiple']
            response_dict = {}
            for prompt in system_prompts:
                # 默认不保存中间处理图像
                response = self._process_image_comparison(resized_frame, frame_path, standard_image_path, prompt)
                response_dict[prompt] = response
        """
        
        # 新的混合处理方式：垃圾占比使用大模型，大件垃圾使用YOLO小模型
        if system_type == 'system_prompt_bucket_shaft':
            # 处理单图耙斗井
            system_prompts = ['system_prompt_waste_percentage']  # 只处理垃圾占比
            response_dict = {}
            for prompt in system_prompts:
                # 默认不保存中间处理图像
                response = self._process_image_comparison(resized_frame, frame_path, standard_image_path, prompt)
                response_dict[prompt] = response
            
            # 使用YOLO模型检测大件垃圾
            bulky_waste_results = self.bucket_shaft_detector.detect_devices(
                resized_frame, save_result=self.save_bucket_shaft_result
            )
            
        elif system_type == 'system_prompt_bucket_shaft_multiple':
            # 处理多图耙斗井
            system_prompts = ['system_prompt_waste_percentage_multiple']  # 只处理垃圾占比
            response_dict = {}
            for prompt in system_prompts:
                # 默认不保存中间处理图像
                response = self._process_image_comparison(resized_frame, frame_path, standard_image_path, prompt)
                response_dict[prompt] = response
            
            # 使用YOLO模型检测大件垃圾
            bulky_waste_results = self.bucket_shaft_detector.detect_devices(
                resized_frame, save_result=self.save_bucket_shaft_result
            )
        
        # 处理分析结果
        result_string = '视觉模型的分析结果为:'
        analysis_result = '调整建议为:'
        
        """
        # 原有的大模型结果处理方式（已注释，保留备用）
        # 提取垃圾占比和大件垃圾的结果
        for i, (key, value) in enumerate(response_dict.items()):
            if i == 0:
                # 垃圾占比检测结果
                items = list(value.items())[:-1]  # 排除最后一个键值对
                result_string = result_string + '\n' + f"垃圾占比检测结果: {'; '.join([f'{k}: {v}' for k, v in items])}"
                last_key, last_value = list(value.items())[-1]  # 获取最后一个键值对
                analysis_result = analysis_result + '\n' + f"垃圾占比调整建议:{last_value}"
                
                value1 = value['垃圾占比']
                number = re.search(r'\d+(\.\d+)?', value1)
                if number is None:
                    logging.warning(f"无法从垃圾占比中提取数字: {value1}")
                    value1 = 0
                else:
                    value1 = float(number.group(0))
                value1, is_abnormal = self.processor._determine_alarm_status(value1, threshold)
            elif i == 1:
                # 大件垃圾检测结果
                items = list(value.items())[:-1]  # 排除最后一个键值对
                result_string = result_string + '\n' + f"大件垃圾检测结果: {'; '.join([f'{k}: {v}' for k, v in items])}"
                last_key, last_value = list(value.items())[-1]  # 获取最后一个键值对
                analysis_result = analysis_result + '\n' + f"大件垃圾调整建议:{last_value}"
                
                value2 = value['是否有大件垃圾']
        """
        
        # 新的混合结果处理方式：垃圾占比来自大模型，大件垃圾来自YOLO
        # 处理垃圾占比检测结果
        if response_dict:
            key, value = list(response_dict.items())[0]  # 获取第一个（也是唯一的）键值对
            # 垃圾占比检测结果
            items = list(value.items())[:-1]  # 排除最后一个键值对
            result_string = result_string + '\n' + f"垃圾占比检测结果: {'; '.join([f'{k}: {v}' for k, v in items])}"
            last_key, last_value = list(value.items())[-1]  # 获取最后一个键值对
            analysis_result = analysis_result + '\n' + f"垃圾占比调整建议:{last_value}"
            
            value1 = value['垃圾占比']
            number = re.search(r'\d+(\.\d+)?', value1)
            if number is None:
                logging.warning(f"无法从垃圾占比中提取数字: {value1}")
                value1 = 0
            else:
                value1 = float(number.group(0))
            value1, is_abnormal = self.processor._determine_alarm_status(value1, threshold)
        else:
            # 如果没有垃圾占比检测结果，设置默认值
            value1 = 0
            is_abnormal = False
        
        # 处理YOLO大件垃圾检测结果
        devices = bulky_waste_results.get('devices', [])
        has_bulky_waste = any(device.get('status') == 'bulky-waste' for device in devices)
        
        if has_bulky_waste:
            # bulky_waste_info = [f"大件垃圾{device['id']}: {device['status']} (置信度: {device['confidence']:.2f})" 
            #                    for device in devices if device.get('status') == 'bulky-waste']
            # result_string += '\n' + f"大件垃圾检测结果: 检测到大件垃圾 - {'; '.join(bulky_waste_info)}"
            result_string += '\n' + f"大件垃圾检测结果: 检测到大件垃圾"
            analysis_result += '\n' + "大件垃圾调整建议: 发现大件垃圾，建议立即清理以避免设备损坏"
            value2 = '有'
        else:
            result_string += '\n' + "大件垃圾检测结果: 未检测到大件垃圾"
            analysis_result += '\n' + "大件垃圾调整建议: 无需处理"
            value2 = '无'
        
        logging.info(f"耙斗井YOLO检测结果: {bulky_waste_results}")
        logging.info(f"检测到的设备数量: {len(devices)}, 大件垃圾: {has_bulky_waste}")
        
        # 确定警报状态
        if value1 == 'WARNING' or value2 == '有':
            coverage_float = 99
            if value1 == 'WARNING':
                failure_reasons_type.append('垃圾占比过高')
            if value2 == '有':
                failure_reasons_type.append('大件垃圾')
        else:
            coverage_float = 1
        
        all_situation_analysis = result_string
        
        # 确定警报状态
        alarm_status_flag, is_abnormal = self._determine_alarm_status(coverage_float, threshold)
        
        logging.info(f"耙斗井处理器处理完成 - 摄像头ID: {camera_id}, 警报状态: {alarm_status_flag}")
        if failure_reasons_type:
            logging.warning(f"检测到故障类型: {', '.join(failure_reasons_type)}")
        
        return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                str(frame_path), analysis_result, failure_reasons_type) 