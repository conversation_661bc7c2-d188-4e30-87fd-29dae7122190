import logging
import re
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Any

from server.utils.handlers.base_handler import BaseFrameHandler
from llms.llm_api_server import llm_process_image_filter


class FilterHandler(BaseFrameHandler):
    """滤池处理器类
    
    负责处理滤池相关的视频帧分析
    """
    
    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理滤池相关的视频帧
        
        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"滤池处理器开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}")
        
        # 先准备帧但不保存，用于分析
        resized_frame, potential_frame_path = self._prepare_frame_without_save(frame, save_dir, camera_id, current_time)
        failure_reasons_type = []  # 出现故障的类型
        
        # 处理图像比较 - 使用临时路径进行分析
        response_dict = self._process_image_comparison(
            resized_frame, potential_frame_path, standard_image_path, system_type
        )
        
        # 分析结果
        analysis_result = llm_process_image_filter(filter_result=response_dict.get('你的思考', ''), system_type=system_type)
        value = response_dict['曝气头是否脱落或损坏']
        logging.info(f"---------------------------")
        logging.info(f"曝气头是否脱落或损坏: {value}")
        logging.info(f"---------------------------")
        
        # 根据分析结果决定是否保存图片
        if value == '是':
            # 异常情况，保存图片
            coverage_float = 99
            failure_reasons_type.append('曝气头可能出现脱落或损坏')
            # 保存图片
            save_success = self._save_frame(resized_frame, potential_frame_path)
            if save_success:
                frame_path = str(potential_frame_path)
                logging.info(f"检测到异常，已保存图片: {frame_path}")
            else:
                frame_path = ''
                logging.error(f"异常情况下保存图片失败")
        else:
            # 正常情况，不保存图片
            coverage_float = 1
            frame_path = ''  # 正常情况不保存图片，路径为空
            logging.info(f"滤池检测结果正常，不需要未保存图片")
            
        all_situation_analysis = response_dict.get('你的思考', '')
        
        # 确定警报状态
        alarm_status_flag, is_abnormal = self._determine_alarm_status(coverage_float, threshold)
        
        logging.info(f"滤池处理器处理完成 - 摄像头ID: {camera_id}, 覆盖率: {coverage_float}, 警报状态: {alarm_status_flag}, 图片路径: {frame_path}")
        if failure_reasons_type:
            logging.warning(f"检测到故障类型: {', '.join(failure_reasons_type)}")
        
        return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                frame_path, analysis_result, failure_reasons_type) 