import logging
import os
import time  # 添加time模块导入
from io import BytesIO
from pathlib import Path
from typing import Dict
import re
import cv2
import numpy as np
import requests
from PIL import Image
from dotenv import load_dotenv
from openai import OpenAI
from config_file import config as config_video
from llms.config import SYSTEM
from llms.config_filter import SYSTEM_FILTER
from llms.config_aerobic import SYSTEM_AEROBIC
from llms.vllm_api_server import process_image
from llms.vllm_api_server_multiple import process_image_multiple
from llms.llm_api_server import llm_process_image_filter, llm_process_image_aerobic
from llms.yolo_api_server import DeviceDetector
from llms.yolo_region import YoloRegionCounter
from server.utils.handlers import HandlerFactory
from llms.yolo_api_server_filter import detector
# from server.task.water_level_comparison import RobotWaterLevelMonitor
"""
视频帧处理模块

主要功能：
1. 处理视频帧图像
2. 分析泡沫覆盖率
3. 生成分析报告和建议
4. 管理图片存储
"""


class VideoFrameProcessor:
    """视频帧处理器类
    
    负责处理视频帧并进行智能分析
    
    Attributes:
        base_dataset_path (Path): 数据集基础路径
        openai_client (OpenAI): OpenAI客户端实例
        openai_model (str): 使用的模型名称
        coverage_thresholds (dict): 覆盖率阈值配置
        coverage_level_names (dict): 覆盖等级名称配置
    """

    def __init__(self, config: dict):
        """初始化视频帧处理器
        
        Args:
            config (dict): 配置字典，包含：
                - storage.paths.base_dataset: 数据集路径
                - coverage_levels: 覆盖率相关配置
        
        Raises:
            EnvironmentError: 环境变量加载失败时
            RuntimeError: GLM客户端初始化失败时
        """
        # 记录初始化开始
        logging.info("开始初始化VideoFrameProcessor")
        
        # 获取当前文件所在目录的父目录（项目根目录）
        project_root = Path(__file__).parent.parent.parent
        
        # 加载环境变量
        env_path = project_root / config_video.args.env_file
        logging.debug(f"尝试加载环境变量文件: {env_path}")
        if not load_dotenv(env_path):
            logging.error(f"无法加载环境变量文件: {env_path}")
            raise EnvironmentError("无法加载环境变量文件")

        # 验证必要的环境变量
        required_vars = ['GLM_API_KEY', 'GLM_BASE_URL', 'GLM_MODEL',
                         'QWEN2_VL_API_KEY', 'QWEN2_VL_BASE_URL', 'QWEN2_VL_MODEL']
        missing_vars = [var for var in required_vars if not config_video.env_vars.get(var)]
        if missing_vars:
            logging.error(f"缺少必要的环境变量: {', '.join(missing_vars)}")
            raise EnvironmentError(f"缺少必要的环境变量: {', '.join(missing_vars)}")
        
        logging.info("环境变量加载成功")

        self.base_dataset_path = Path(config['storage']['paths']['base_dataset'])
        logging.debug(f"设置数据集基础路径: {self.base_dataset_path}")
        
        # 从配置中获取是否保存中间图像的设置
        self.save_intermediate_images = config.get('storage', {}).get('image_processing', {}).get('save_intermediate_images', False)
        logging.info(f"中间处理图像保存设置: {'启用' if self.save_intermediate_images else '禁用'}")

        # 初始化OpenAI客户端
        try:
            self.openai_client = OpenAI(
                api_key=config_video.env_vars.get('GLM_API_KEY'),
                base_url=config_video.env_vars.get('GLM_BASE_URL')
            )
            self.openai_model = config_video.env_vars.get('GLM_MODEL')
            logging.info(f"大模型客户端初始化成功，使用模型: {self.openai_model}")
        except Exception as e:
            logging.error(f"初始化大模型客户端失败: {str(e)}")
            raise RuntimeError(f"初始化GLM客户端失败: {str(e)}")

        # 添加覆盖率阈值的配置
        self.coverage_thresholds = config.get('coverage_levels', {}).get('thresholds', {
            'low': 25,
            'medium': 50,
            'high': 100
        })
        self.coverage_level_names = config.get('coverage_levels', {}).get('levels', {
            'low': 'LOW',
            'medium': 'MEDIUM',
            'high': 'HIGH'
        })
        logging.debug(f"覆盖率阈值配置: {self.coverage_thresholds}")
        logging.debug(f"覆盖等级名称配置: {self.coverage_level_names}")
        
        self.region_points = {
        "region-01": [(160, 300), (600, 300), (600, 0), (160, 0)],  # 向右移动100像素
        "region-02": [(600, 300), (1100, 300), (1100, 0), (600, 0)],  # 向右移动100像素
        "region-03": [(1100, 300), (1500, 300), (1500, 0), (1100, 0)],  # 向右移动100像素
        }
        logging.debug(f"区域点配置: {list(self.region_points.keys())}")
        
        # 创建检测器实例
        logging.info("初始化设备检测器")
        self.detector = DeviceDetector()
        logging.info("初始化区域计数器")
        # 这里不要初始化,否则可能卡住,暂时还不知道为什么
        # self.regioncounter = YoloRegionCounter(
        #     # image_path=image_path, # 不填写代表的是不使用预定的图像文件
        #     region_points=self.region_points,
        #     model_path="llms/models/yolo/best-dipper.pt",
        #     show=False  # 在无界面环境下设为False
        # )
        self.yolo_model = config.get('yolo_model', 'llms/models/yolo/best-dipper.pt')
        logging.debug(f"YOLO模型路径: {self.yolo_model}")
        
        # 初始化滤池YOLO检测器
        from llms.yolo_api_server_filter import DeviceDetector as FilterDetector
        self.filter_detector = FilterDetector(model_path=config.get('yolo_model_filter', 'llms/models/yolo/best-filter.pt'))
        logging.info("初始化滤池检测器完成")
        
        # 获取YOLO检测结果保存配置
        yolo_save_result = config.get('yolo_save_result', {})
        self.save_bucket_dipper_result = yolo_save_result.get('bucket_dipper', True)  # 默认保存耙斗检测结果
        self.save_filter_result = yolo_save_result.get('filter', False)  # 默认不保存滤池检测结果
        self.save_region_counter_result = yolo_save_result.get('region_counter', True)  # 默认保存区域计数结果
        logging.info(f"YOLO检测结果保存配置: 耙斗={self.save_bucket_dipper_result}, 滤池={self.save_filter_result}, 区域计数={self.save_region_counter_result}")
        
        # 设置检测模式：可选 "frame"(仅帧数), "time"(仅时间), "both"(两者)
        self.detection_mode = config.get('detection_mode', 'both')  # 默认使用两者结合的判断模式
        logging.info(f"初始化检测模式: {self.detection_mode} (frame=仅帧数, time=仅时间, both=两者结合)")
        
        # 为每个区域添加计时器和状态跟踪
        self.region_state = {
            "region-01": {"last_state": None, "timer": 0, "alarm_triggered": False, "last_change_time": None, "reset_counter": 0},
            "region-02": {"last_state": None, "timer": 0, "alarm_triggered": False, "last_change_time": None, "reset_counter": 0},
            "region-03": {"last_state": None, "timer": 0, "alarm_triggered": False, "last_change_time": None, "reset_counter": 0}
        }
        # 设置耙斗卡住判断阈值（单位：帧数）
        self.stuck_threshold = config.get('stuck_threshold', 30)  # 默认30帧
        logging.debug(f"耙斗卡住判断阈值(帧数): {self.stuck_threshold}")
        # 设置耙斗卡住判断的时间阈值（单位：秒）
        self.stuck_time_threshold = config.get('stuck_time_threshold', 3.0)  # 默认3秒
        logging.debug(f"耙斗卡住判断时间阈值(秒): {self.stuck_time_threshold}")
        
        # 重置机制配置
        self.reset_enabled = config.get('reset_enabled', True)  # 默认启用重置机制
        # 设置重置阈值，超过此阈值后重置检测状态（单位：帧数）
        self.reset_threshold = config.get('reset_threshold', 120) if self.reset_enabled else False  # 默认120帧
        # 设置重置的时间阈值（单位：秒）
        self.reset_time_threshold = config.get('reset_time_threshold', 12.0) if self.reset_enabled else False  # 默认12秒
        logging.info(f"重置机制: 启用={self.reset_enabled}, 帧阈值={self.reset_threshold}帧, 时间阈值={self.reset_time_threshold}秒")
        
        # 添加设备倾斜状态跟踪
        self.device_incline_state = {
            1: {"alarm_triggered": False, "last_status": "unknown", "last_change_time": None, "reset_counter": 0},
            2: {"alarm_triggered": False, "last_status": "unknown", "last_change_time": None, "reset_counter": 0},
            3: {"alarm_triggered": False, "last_status": "unknown", "last_change_time": None, "reset_counter": 0}
        }
        # 添加报警重置计数器 - 分开控制倾斜和卡住
        self.incline_reset_counter = 0  # 保留全局计数器以兼容旧代码
        self.stuck_reset_counter = 0
        # 添加报警重置的时间记录
        self.last_incline_reset_time = time.time()
        self.last_stuck_reset_time = time.time()
        
        # 获取是否启用各种重置功能的配置
        # 倾斜检测重置配置
        self.incline_reset_enabled = config.get('incline_reset_enabled', 
                                              config.get('alarm_reset_enabled', True))  # 默认启用
        self.incline_reset_threshold = config.get('incline_reset_threshold', 
                                              config.get('alarm_reset_threshold', 300))  # 默认阈值300帧
        # 倾斜检测重置的时间阈值（单位：秒）
        self.incline_reset_time_threshold = config.get('incline_reset_time_threshold', 
                                                   config.get('alarm_reset_time_threshold', 30.0))  # 默认30秒
        
        # 卡住检测重置配置
        self.stuck_reset_enabled = config.get('stuck_reset_enabled', 
                                            config.get('alarm_reset_enabled', True))  # 默认启用
        self.stuck_reset_threshold = config.get('stuck_reset_threshold', 
                                              config.get('alarm_reset_threshold', 300))  # 默认阈值300帧
        # 卡住检测重置的时间阈值（单位：秒）
        self.stuck_reset_time_threshold = config.get('stuck_reset_time_threshold', 
                                                 config.get('alarm_reset_time_threshold', 30.0))  # 默认30秒
        
        # 保留旧的参数用于兼容
        self.alarm_reset_enabled = config.get('alarm_reset_enabled', True)  # 默认启用
        self.alarm_reset_threshold = config.get('alarm_reset_threshold', 300)  # 默认阈值300帧
        self.alarm_reset_time_threshold = config.get('alarm_reset_time_threshold', 30.0)  # 默认30秒
        
        # 新增耙斗倾斜检测频率控制
        self.incline_detection_frequency = config.get('incline_detection_frequency', 60)  # 默认每3次卡住检测执行1次倾斜检测
        logging.info(f"耙斗倾斜检测频率: 每{self.incline_detection_frequency}次卡住检测执行1次倾斜检测")
        
        # 初始化耙斗倾斜检测计数器
        self.incline_detection_counter = 0
        
        logging.info(f"初始化报警重置机制: 倾斜检测重置(启用={self.incline_reset_enabled}, 帧阈值={self.incline_reset_threshold}帧, 时间阈值={self.incline_reset_time_threshold}秒), "
                    f"卡住检测重置(启用={self.stuck_reset_enabled}, 帧阈值={self.stuck_reset_threshold}帧, 时间阈值={self.stuck_reset_time_threshold}秒)")

        # 存储每个摄像头的上一次泡沫面积
        self.previous_bubble_area = {}
        # 存储每个摄像头的泡沫面积增加次数
        self.bubble_increase_counter = {}
        # 设置连续增加触发报警的阈值
        self.bubble_increase_threshold = config.get('bubble_increase_threshold', 2)  # 默认连续3次
        
        # 初始化处理器工厂
        self.handler_factory = HandlerFactory(self)
        logging.info("初始化处理器工厂完成")

        logging.info("VideoFrameProcessor初始化完成")

    def process_frame_filter(self, frame, frame_count, save_dir, camera_id, sensor_data,
                    threshold, system_type, standard_image_path=None, current_time=None) -> tuple:
        """处理单个视频帧并返回分析结果
        
        Args:
            frame (np.ndarray): 视频帧图像数据
            frame_count (int): 帧计数
            save_dir (Path): 保存目录
            camera_id (str): 摄像头ID
            sensor_data (Dict): 传感器数据
            threshold (float): 覆盖率阈值
            system_type (str): 系统类型
            standard_image_path (str, optional): 标准图片路径
            current_time (datetime, optional): 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}, 系统类型: {system_type}")
        
        try:
            # 通过处理器工厂获取对应的处理器
            handler = self.handler_factory.get_handler(system_type)
            
            # 使用处理器处理帧
            result = handler.process_frame(
                frame, frame_count, save_dir, camera_id, sensor_data,
                threshold, system_type, standard_image_path, current_time
            )
            
            logging.info(f"帧处理完成 - 摄像头ID: {camera_id}, 覆盖率: {result[0]}, 警报状态: {result[2]}, 异常: {result[3]}")
            if result[6]:  # 检查故障类型列表
                logging.warning(f"检测到故障类型: {', '.join(result[6])}")
            
            return result
            
        except Exception as e:
            logging.error(f"处理帧时发生错误: {str(e)}", exc_info=True)
            # 返回默认值
            return (0, "处理失败", 0, False, "", "处理过程中出现错误", [])

    def _process_image_comparison(self, frame, frame_path, standard_image_path, system_type, save_intermediate_images=False):
        """处理图像比较
        
        Args:
            frame: 当前帧图像
            frame_path: 帧图像保存路径
            standard_image_path: 标准图像路径
            system_type: 系统类型
            
        Returns:
            dict: 图像分析结果
        """
        logging.info(f"开始图像处理和比较 - 系统类型: {system_type}, 标准参考图像: {'有' if standard_image_path else '无'}")
        
        if standard_image_path: # 有标准图片使用多图的识别逻辑
            standard_image_filename = Path(standard_image_path).name
            local_image_path = self._save_standard_image(standard_image_path, standard_image_filename)
            frames = [cv2.imread(str(local_image_path)), frame]
            logging.info(f"---------------------------")
            logging.info(f"多图提示词是：: {system_type}")
            logging.info(f"---------------------------")
            return process_image_multiple(frames, system_type)
        elif system_type == 'system_prompt_bucket_dipper':
            # 检查是否在测试模式下使用测试图片
            # if hasattr(self, 'test_mode') and self.test_mode and hasattr(self, 'current_test_image_index'):
            #     # 使用测试图片
            #     test_image_path = self.test_images[self.current_test_image_index]
            #     logging.info(f"---------------------------")
            #     logging.info(f"使用测试图片: {test_image_path}")
            #     logging.info(f"---------------------------")
            #     image = cv2.imread(test_image_path)
            #     self.current_test_image_index = (self.current_test_image_index + 1) % len(self.test_images)
            # else:
            image = frame  # 使用帧numpy格式的数据处理
            
            # 1. 创建区域计数器
            regioncounter = YoloRegionCounter(
                region_points=self.region_points,
                model_path="llms/models/yolo/best-dipper.pt",
                show=False  # 在无界面环境下设为False
            )
            
            # 2. 初始化倾斜检测结果
            incline_results = {'devices': []}  # 默认为空列表
            
            # 3. 增加计数器并判断是否需要执行倾斜检测
            self.incline_detection_counter += 1
            run_incline_detection = self.incline_detection_counter >= self.incline_detection_frequency
            
            # 4. 如果需要执行倾斜检测，则调用检测器
            if run_incline_detection:
                logging.info(f"执行耙斗倾斜检测 - 计数器: {self.incline_detection_counter}/{self.incline_detection_frequency}")
                incline_results = self.detector.detect_devices(image, save_result=self.save_bucket_dipper_result)
                # 重置计数器
                self.incline_detection_counter = 0
                is_incline_detection_skipped = False
            else:
                logging.info(f"跳过耙斗倾斜检测 - 计数器: {self.incline_detection_counter}/{self.incline_detection_frequency}")
                is_incline_detection_skipped = True
            
            # 5. 始终执行卡住检测
            try:
                image_results = regioncounter.process_frame(image, save_output=self.save_region_counter_result)
                logging.info(f"---------------------------")
                logging.info(f'区域计数结果: {image_results}')
                logging.info(f"---------------------------")
            finally:
                regioncounter._close_regioncounter()
                logging.info("区域计数识别完成，资源已释放")
            
            # 6. 记录倾斜检测结果日志
            if run_incline_detection:
                logging.info(f"---------------------------")
                logging.info(f"yolo识别结果是: {incline_results}")
                logging.info(f"---------------------------")
            
            # 7. 合并结果
            combined_results = {
                'devices': incline_results.get('devices', []),  # 可能是空的，也可能有内容
                'region_counts': image_results.region_counts,
                'total_tracks': image_results.total_tracks,
                'is_incline_detection_skipped': is_incline_detection_skipped  # 添加标识字段
            }
            
            return combined_results
        else: 
            # 没有标准图片使用单图的识别逻辑
            if system_type in ['system_prompt_filter1','system_prompt_filter_multiple']:
                # 使用YOLO模型进行滤池识别
                
                # 调用滤池YOLO检测器
                results_path = self.filter_detector.detect_devices(frame, save_result=self.save_filter_result)
                logging.info(f"---------------------------")
                logging.info(f"滤池YOLO识别结果: {results_path}")
                logging.info(f"---------------------------")
                
                # 解析YOLO检测结果
                devices = results_path.get('devices', [])
                has_malfunction = any(device.get('status') == 'malfunction' for device in devices)
                
                # 根据检测结果生成返回值
                if has_malfunction:
                    process_image_result = {
                        "你的思考": "画面中发现一处或多处水面鼓起，可能是曝气头松动、脱落或损坏造成的。",
                        "是否反冲洗": "是",
                        "曝气头是否脱落或损坏": "是",
                        "调整建议": "1. 先暂停该滤池的反冲洗；\n2. 检查所有鼓起位置的曝气头，必要时更换并固定好；\n3. 重新启动滤池，观察水面翻滚是否恢复正常。"
                    }
                else:
                    process_image_result = {
                        "你的思考": "画面中未发现曝气头故障，运行正常。",
                        "是否反冲洗": "否",
                        "曝气头是否脱落或损坏": "否",
                        "调整建议": "保持现状，无需处理。"
                    }
                
                return process_image_result
                
                # 以下是原有的大模型代码，现在注释保留
                """
                # 使用assets中的滤池基准图像作为参考
                base_image = cv2.imread("assets/filter_aeration_failure_images.png")
                if base_image is None:
                    logging.error("无法加载基准图像：assets/filter_aeration_failure_images.png")
                    return {"错误": "无法加载基准图像"}
                # 构建包含基准图像和当前图像的列表
                frames = [base_image, frame]
                logging.info("第一张图片路径是: 'assets/filter_aeration_failure_images.png'")
                logging.info(f"第二张图片路径是: {frame_path}")
                process_image_result = process_image_multiple(frames, system_type)
                return process_image_result
                """
            else:
                # 这里需要将曝气头脱离以及泡沫面积拆分出来,然后分别调用两次模型
                if system_type in ['system_prompt_aerobic_single1', 'system_prompt_aerobic_single2']:
                    # 根据不同的系统类型，选择不同的提示词及坐标文件进行处理
                    air_bubble_frame_path = None
                    bubble_area_frame_path = None
                    
                    # 保存原始图像，为透视变换和分割做准备
                    original_frame_path = str(frame_path)
                    
                    # 创建处理后图像的保存路径
                    processed_dir = Path(original_frame_path).parent / 'processed'
                    processed_dir.mkdir(parents=True, exist_ok=True)
                    
                    # 获取原始文件名和扩展名
                    original_filename = Path(original_frame_path).stem
                    original_ext = Path(original_frame_path).suffix
                    
                    # 根据系统类型选择不同的坐标文件和处理方法
                    if system_type == 'system_prompt_aerobic_single1':
                        # 前段 - 曝气头检测 - 仅需分割不需透视变换
                        from server.utils.image_extractor import extract_from_image
                        
                        air_bubble_coords_file = 'llms/utils/split_coords/Aerobic_pool_front-or_coords.txt'
                        air_bubble_frame_path = str(processed_dir / f"{original_filename}_front_air_bubble{original_ext}")
                        
                        # 分割图像用于曝气头检测 - 根据参数决定是否保存文件
                        air_bubble_image = extract_from_image(
                            image_path=original_frame_path,
                            coordinates=air_bubble_coords_file,
                            save=self.save_intermediate_images,  # 使用类属性
                            output_path=air_bubble_frame_path if self.save_intermediate_images else None,
                            trim_border=False
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已分割好氧池前段图像用于曝气头检测并保存至: {air_bubble_frame_path}")
                        else:
                            logging.info(f"已分割好氧池前段图像用于曝气头检测")
                        
                        # 泡沫面积评估需要透视变换
                        from server.utils.image_extractor_aerobic import transform_image_with_coords
                        
                        bubble_area_coords_file = 'llms/utils/split_coords/Aerobic_pool_front.txt'
                        bubble_area_frame_path = str(processed_dir / f"{original_filename}_front_bubble_area{original_ext}")
                        
                        # 透视变换图像用于泡沫面积评估 - 根据参数决定是否保存文件
                        bubble_area_image, saved_path = transform_image_with_coords(
                            image_path=original_frame_path,
                            coords_file_path=bubble_area_coords_file,
                            save_result=self.save_intermediate_images,  # 使用类属性
                            output_path=bubble_area_frame_path if self.save_intermediate_images else None
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已透视变换好氧池前段图像用于泡沫面积评估并保存至: {bubble_area_frame_path}")
                        else:
                            logging.info(f"已透视变换好氧池前段图像用于泡沫面积评估")
                        
                    else:  # system_prompt_aerobic_single2
                        # 中段 - 曝气头检测 - 仅需分割不需透视变换
                        from server.utils.image_extractor import extract_from_image
                        
                        air_bubble_coords_file = 'llms/utils/split_coords/Aerobic_pool_rear_section-or_coords.txt'
                        air_bubble_frame_path = str(processed_dir / f"{original_filename}_rear_air_bubble{original_ext}")
                        
                        # 分割图像用于曝气头检测 - 根据参数决定是否保存文件
                        air_bubble_image = extract_from_image(
                            image_path=original_frame_path,
                            coordinates=air_bubble_coords_file,
                            save=self.save_intermediate_images,  # 使用类属性
                            output_path=air_bubble_frame_path if self.save_intermediate_images else None,
                            trim_border=False
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已分割好氧池中段图像用于曝气头检测并保存至: {air_bubble_frame_path}")
                        else:
                            logging.info(f"已分割好氧池中段图像用于曝气头检测")
                        
                        # 泡沫面积评估需要透视变换
                        from server.utils.image_extractor_aerobic import transform_image_with_coords
                        
                        bubble_area_coords_file = 'llms/utils/split_coords/Aerobic_pool_rear_section.txt'
                        bubble_area_frame_path = str(processed_dir / f"{original_filename}_rear_bubble_area{original_ext}")
                        
                        # 透视变换图像用于泡沫面积评估 - 根据参数决定是否保存文件
                        bubble_area_image, saved_path = transform_image_with_coords(
                            image_path=original_frame_path,
                            coords_file_path=bubble_area_coords_file,
                            save_result=self.save_intermediate_images,  # 使用类属性
                            output_path=bubble_area_frame_path if self.save_intermediate_images else None
                        )
                        if self.save_intermediate_images:
                            logging.info(f"已透视变换好氧池中段图像用于泡沫面积评估并保存至: {bubble_area_frame_path}")
                        else:
                            logging.info(f"已透视变换好氧池中段图像用于泡沫面积评估")
                    
                    # 处理错误情况
                    if air_bubble_image is None or bubble_area_image is None:
                        logging.error(f"图像处理失败，无法进行分析")
                        return {
                            "当前泡沫覆盖率": "0%",
                            "情况分析": "图像处理失败，无法进行分析",
                            "曝气头是否脱落或者损坏": "否"
                        }
                    
                    # 使用处理后的图像直接调用模型，无论是否保存了中间文件
                    if system_type == 'system_prompt_aerobic_single1':
                        # 第一次调用模型检测曝气头状态 - 直接传递图像数组而非文件路径
                        air_bubble_result = process_image(air_bubble_image, 'system_prompt_aerobic_single1_air_bubble')
                        # 第二次调用模型检测泡沫面积 - 直接传递图像数组而非文件路径
                        bubble_area_result = process_image(bubble_area_image, 'system_prompt_aerobic_single1_bubble_area')
                    else:  # system_prompt_aerobic_single2
                        # 第一次调用模型检测曝气头状态 - 直接传递图像数组而非文件路径
                        air_bubble_result = process_image(air_bubble_image, 'system_prompt_aerobic_single2_air_bubble')
                        # 第二次调用模型检测泡沫面积 - 直接传递图像数组而非文件路径
                        bubble_area_result = process_image(bubble_area_image, 'system_prompt_aerobic_single2_bubble_area')
                    
                    # 合并曝气头和泡沫面积的情况分析
                    air_bubble_analysis = air_bubble_result.get("情况分析", "")
                    bubble_area_analysis = bubble_area_result.get("情况分析", "")
                    
                    # 综合分析结果
                    combined_analysis = ""
                    if air_bubble_analysis and bubble_area_analysis:
                        combined_analysis = f"曝气头状态分析：{air_bubble_analysis}\n\n泡沫面积分析：{bubble_area_analysis}"
                    elif air_bubble_analysis:
                        combined_analysis = f"曝气头状态分析：{air_bubble_analysis}"
                    elif bubble_area_analysis:
                        combined_analysis = f"泡沫面积分析：{bubble_area_analysis}"
                    
                    process_image_result = {
                        "当前泡沫覆盖率": bubble_area_result.get("当前泡沫覆盖率", "0%"),
                        "情况分析": combined_analysis,
                        "曝气头是否脱落或者损坏": air_bubble_result.get("曝气头是否脱落或损坏", "否")
                    }
                    
                    # 记录日志，方便调试
                    logging.info(f"好氧池分析 - 曝气头结果: {air_bubble_result}")
                    logging.info(f"好氧池分析 - 泡沫面积结果: {bubble_area_result}")
                    logging.info(f"好氧池分析 - 合并结果: {process_image_result}")
                    
                    return process_image_result
                else:
                    process_image_result = process_image(str(frame_path), system_type)
                    return process_image_result
        # else: # 没有标准图片使用yolo的识别逻辑
        #     return DeviceDetector().detect_devices(frame)

    def _save_standard_image(self, standard_image_path: str, filename: str) -> Path:
        """保存标准图像
        
        下载并保存标准图像，如果已存在则直接返回路径
        
        Args:
            standard_image_path (str): 标准图像URL
            filename (str): 保存的文件名
            
        Returns:
            Path: 保存后的图像路径
        """
        local_image_path = self.base_dataset_path / 'standard_images' / filename
        if not local_image_path.exists():
            local_image_path.parent.mkdir(parents=True, exist_ok=True)
            response = requests.get(standard_image_path)
            if response.status_code == 200:
                image = Image.open(BytesIO(response.content))
                image.save(str(local_image_path))
                logging.info(f"保存新的标准图片: {local_image_path}")
        return local_image_path



    def _determine_alarm_status(self, coverage_float: float, threshold: float) -> tuple:
        """确定警报状态
        
        Args:
            coverage_float (float): 覆盖率
            threshold (float): 阈值
            
        Returns:
            tuple: (警报状态, 是否异常)
                - 警报状态: 'WARNING' 或 'NO_ALARM'
                - 是否异常: True 或 False
        """
        is_abnormal = coverage_float > threshold
        alarm_status = 'WARNING' if is_abnormal else 'NO_ALARM'
        logging.debug(f"确定警报状态 - 覆盖率: {coverage_float}, 阈值: {threshold}, 结果: {alarm_status}")
        return (alarm_status, is_abnormal)

    def determine_coverage_level(self, coverage_percentage: float=0) -> str:
        """原始根据泡沫覆盖率确定覆盖等级--》如果没有泡沫面积输入,就根据其他判断
        
        Args:
            coverage_percentage (float): 覆盖率百分比(0-100)
            
        Returns:
            str: 覆盖等级(LOW/MEDIUM/HIGH)
            
        Raises:
            ValueError: 当覆盖率值超出有效范围时
        """
        if not 0 <= coverage_percentage <= 100:
            logging.error(f"覆盖率值超出有效范围: {coverage_percentage}")
            raise ValueError("覆盖率值超出有效范围 (0-100%)")

        if 0 <= coverage_percentage <= self.coverage_thresholds['low']:
            level = self.coverage_level_names['low']
        elif coverage_percentage <= self.coverage_thresholds['medium']:
            level = self.coverage_level_names['medium']
        else:
            level = self.coverage_level_names['high']
            
        logging.debug(f"确定覆盖等级 - 覆盖率: {coverage_percentage}%, 结果: {level}")
        return level
    def alarm_status(self, alarm_status_str: str='') -> str:
        """根据其他状态判断报警信息
        
        Args:
            alarm_status_str (str): 其他状态
            
        Returns:
            str: 报警信息
            
        Raises:

        """
        
        if alarm_status_str == 1:
            result = ('HIGH', 'WARNING')
            logging.debug(f"确定报警状态 - 输入: {alarm_status_str}, 结果: HIGH/WARNING")
            return result
        else:
            result = ('LOW', 'NO_ALARM')
            logging.debug(f"确定报警状态 - 输入: {alarm_status_str}, 结果: LOW/NO_ALARM")
            return result

    def start_test_mode(self):
        """开始测试模式
        
        Returns:
            bool: 是否成功启动测试模式
        """
        logging.info("启动测试模式")
        self.test_mode = True
        self.current_test_image_index = 0
        return True
        
    def stop_test_mode(self):
        """停止测试模式
        
        Returns:
            bool: 是否成功停止测试模式
        """
        if hasattr(self, 'test_mode'):
            logging.info("停止测试模式")
            self.test_mode = False
            return True
        logging.warning("尝试停止测试模式失败，测试模式未启动")
        return False
