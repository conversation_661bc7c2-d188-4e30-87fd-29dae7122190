import sys
import os
import logging
import time
import threading
import datetime
import cv2
import json
import traceback
import functools
import uuid
from typing import Dict, List, Optional, Tuple, Any, Callable
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from server.utils.ctrl_device import control_slag_pipe, control_slag_weir_gate, control_robot
from server.remote.device import get_robot_braking_status
# 添加YOLO撇渣管识别模型导入
from llms.yolo_region_piezha import YoloOBBRegionDetector
# ==============================================================================
# 1. 外部依赖和基础配置
# ==============================================================================

# 添加项目根目录到系统路径
# 请确保此路径正确，或根据您的项目结构调整
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    from config_file import config
    from server.utils.logger import setup_logging
    from server.utils.pg_tool import insert_image_metadata
    from server.utils.get_cameras_info import get_robot_status_info, get_robot_and_cameras_status
    from server.utils.data_publisher import DataPublisher
    from llms.vllm_api_server import process_image
    from llms.vllm_api_server_multiple import process_image_multiple
    from server.utils.get_alarm_info import get_alarm_info_by_device_id
    
    # 配置日志
    setup_logging()
    
except ImportError as e:
    print(f"导入模块失败，请确保项目结构和依赖正确: {e}")
    # 提供一个备用的config对象，以便代码可以至少被解析
    class MockConfig:
        env = {
            'storage': {'paths': {'base_dataset': 'datasets'}},
            'robot_water_level': {},
            'system': {'thread_pool': {}, 'video_capture': {}},
        }
    config = MockConfig()
    # 手动设置基础日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


logger = logging.getLogger(__name__)

# 导入PyAV库（如果可用）
try:
    import av
    PYAV_AVAILABLE = True
except ImportError:
    PYAV_AVAILABLE = False
    logger.warning("PyAV库未安装，将仅使用OpenCV进行视频帧获取")

# ==============================================================================
# 2. 健壮的视频流抓取器 (ResilientFrameGrabber)
# ==============================================================================

class ResilientFrameGrabber:
    """
    一个健壮的、支持自动重连的视频流抓取器。
    它在后台线程中持续抓取最新帧，以解决RTSP流的延迟问题。
    """
    def __init__(self, rtsp_url: str, reconnect_delay: int = 5):
        self.rtsp_url = rtsp_url
        self.reconnect_delay = reconnect_delay
        self.cap = None
        
        self.latest_frame: Optional[Any] = None
        self.last_read_success: bool = False
        self.lock = threading.Lock()
        self.running: bool = False
        self.thread = threading.Thread(target=self._update, daemon=True)

    def _connect(self) -> bool:
        """尝试连接或重新连接到视频流

        改进的资源管理：
        - 添加更好的异常处理
        - 确保在所有情况下都正确释放资源
        - 防止double free错误
        """
        logger.info(f"正在尝试连接到: {self.rtsp_url}")

        # 安全释放现有连接
        self._safe_release_cap()

        try:
            # 直接使用与test_rtsp.py相同的连接方式，不指定额外参数
            self.cap = cv2.VideoCapture(self.rtsp_url)

            # 添加更多的连接信息日志
            if self.cap.isOpened():
                # 获取视频流的基本信息
                width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = self.cap.get(cv2.CAP_PROP_FPS)

                logger.info(f"成功连接到: {self.rtsp_url}")
                logger.info(f"视频流信息 - 分辨率: {width}x{height}, FPS: {fps}")

                # 尝试设置缓冲区大小
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                return True
            else:
                logger.error(f"无法连接到: {self.rtsp_url}. 将在 {self.reconnect_delay} 秒后重试。")
                self._safe_release_cap()
                return False

        except Exception as e:
            logger.error(f"连接到 {self.rtsp_url} 时发生异常: {e}")
            self._safe_release_cap()
            return False

    def _safe_release_cap(self):
        """安全释放VideoCapture资源，防止double free错误"""
        if self.cap is not None:
            try:
                if hasattr(self.cap, 'isOpened') and self.cap.isOpened():
                    self.cap.release()
                elif hasattr(self.cap, 'release'):
                    # 即使isOpened()失败，也尝试释放
                    self.cap.release()
            except Exception as e:
                logger.warning(f"释放VideoCapture时出现异常（可能是double free）: {e}")
            finally:
                self.cap = None

    def _update(self):
        """线程的目标函数，持续读取帧并处理断连"""
        while self.running:
            try:
                if self.cap is None or not self.cap.isOpened():
                    if not self._connect():
                        time.sleep(self.reconnect_delay)
                        continue
                
                # grab() 只抓取帧不解码，速度更快，有助于清空缓冲区
                grabbed = self.cap.grab()
                
                if grabbed:
                    # 只有在需要时才解码，但为了简单，这里直接解码
                    ret, frame = self.cap.retrieve()
                    if ret:
                        with self.lock:
                            self.last_read_success = True
                            self.latest_frame = frame
                    else:
                        # retrieve失败也认为是断开
                        self._handle_disconnect()
                else:
                    # grab失败意味着连接断开
                    self._handle_disconnect()

            except Exception as e:
                logger.error(f"FrameGrabber线程发生异常: {e}", exc_info=True)
                # 使用安全的资源释放方法
                self._safe_release_cap()
                time.sleep(self.reconnect_delay)

    def _handle_disconnect(self):
        """处理断开连接的通用逻辑

        改进的资源管理：
        - 使用安全的资源释放方法
        - 防止double free错误
        """
        logger.warning(f"从 {self.rtsp_url} 读取帧失败，可能已断开连接。")
        self._safe_release_cap()
        with self.lock:
            self.last_read_success = False
        time.sleep(1) # 短暂休眠后立即尝试重连

    def start(self):
        """启动抓取线程"""
        if self.running: return
        self.running = True
        self.thread.start()
        logger.info(f"ResilientFrameGrabber已为 {self.rtsp_url} 启动")

    def read(self, copy_frame: bool = True) -> Tuple[bool, Optional[Any]]:
        """从外部获取最新的一帧图像

        Args:
            copy_frame: 是否复制帧数据。设为False可以避免内存复制，但调用者需要立即使用帧数据

        Returns:
            tuple: (是否成功, 帧数据)

        注意：
            - 当copy_frame=False时，返回的帧数据是共享的，调用者应立即使用或复制
            - 当copy_frame=True时，返回的是帧数据的副本，安全但消耗更多内存
        """
        with self.lock:
            if not self.last_read_success:
                return False, None

            if copy_frame:
                # 传统模式：复制帧数据，安全但消耗内存
                return self.last_read_success, self.latest_frame.copy()
            else:
                # 优化模式：直接返回帧引用，节省内存但需要立即使用
                return self.last_read_success, self.latest_frame

    def stop(self):
        """停止线程并释放资源

        改进的资源管理：
        - 确保线程安全停止
        - 使用安全的资源释放方法
        - 防止double free错误
        """
        self.running = False

        # 等待线程安全停止
        if self.thread.is_alive():
            self.thread.join(timeout=2.0)
            if self.thread.is_alive():
                logger.warning(f"线程未能在超时时间内停止: {self.rtsp_url}")

        # 安全释放VideoCapture资源
        self._safe_release_cap()
        logger.info(f"ResilientFrameGrabber已为 {self.rtsp_url} 停止")

    def isOpened(self) -> bool:
        """外部检查连接状态的方法"""
        return self.cap is not None and self.cap.isOpened()

# ==============================================================================
# 3. 视频流连接池 (VideoStreamPool)
# ==============================================================================

class VideoStreamPool:
    """
    视频流连接池，管理 ResilientFrameGrabber 实例。
    避免频繁创建和销毁抓取器，并自动清理空闲连接。
    """
    def __init__(self, max_idle_time: int = 300, check_interval: int = 60):
        self.streams: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.RLock()
        self.max_idle_time = max_idle_time
        self.check_interval = check_interval
        self.running = False
        self.cleanup_thread: Optional[threading.Thread] = None
        
    def start(self):
        """启动连接池和清理线程"""
        if self.running: return
        self.running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_idle_streams, daemon=True)
        self.cleanup_thread.start()
        logger.info("视频流连接池已启动")
        
    def stop(self):
        """停止连接池和清理线程"""
        self.running = False
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5.0)
        
        with self.lock:
            for stream_key, stream_info in list(self.streams.items()):
                self._close_stream(stream_key, stream_info)
            self.streams.clear()
        
        logger.info("视频流连接池已停止")
    
    def get_stream(self, rtsp_url: str) -> Optional[ResilientFrameGrabber]:
        """获取一个视频流抓取器实例"""
        with self.lock:
            if rtsp_url in self.streams:
                stream_info = self.streams[rtsp_url]
                stream_info['last_used'] = time.time()
                grabber = stream_info['stream']
                
                if grabber and grabber.running:
                    logger.info(f"复用视频流抓取器: {rtsp_url}")
                    return grabber
                else:
                    logger.warning(f"抓取器无效，将重新创建: {rtsp_url}")
                    if grabber: grabber.stop()
                    del self.streams[rtsp_url]
            
            # 创建新的抓取器实例
            try:
                # 目前只实现了OpenCV的抓取器
                grabber = ResilientFrameGrabber(rtsp_url)
                grabber.start()

                self.streams[rtsp_url] = {
                    'stream': grabber,
                    'last_used': time.time(),
                }
                logger.info(f"创建新的视频流抓取器: {rtsp_url}")
                return grabber
            except Exception as e:
                logger.error(f"创建视频流抓取器时出错: {e}", exc_info=True)
                return None
    
    def _close_stream(self, rtsp_url: str, stream_info: Dict[str, Any]):
        """关闭单个抓取器

        改进的资源清理：
        - 增强异常处理
        - 确保资源完全释放
        - 防止内存泄漏
        """
        try:
            grabber = stream_info.get('stream')
            if grabber is None:
                logger.warning(f"抓取器为空，无需关闭: {rtsp_url}")
                return

            if isinstance(grabber, ResilientFrameGrabber):
                # 停止抓取器
                grabber.stop()
                logger.info(f"已停止并关闭视频流抓取器: {rtsp_url}")

                # 显式删除引用，帮助垃圾回收
                del grabber
            else:
                logger.warning(f"未知的抓取器类型: {type(grabber)}")

        except Exception as e:
            logger.error(f"关闭视频流抓取器时出错: {e}", exc_info=True)
        finally:
            # 确保从stream_info中移除引用
            if 'stream' in stream_info:
                stream_info['stream'] = None
    
    def _cleanup_idle_streams(self):
        """后台线程，用于清理长时间未使用的抓取器

        改进的清理机制：
        - 增强线程同步
        - 添加内存监控
        - 防止资源泄漏
        """
        logger.info("视频流连接池清理线程已启动")
        while self.running:
            try:
                time.sleep(self.check_interval)
                current_time = time.time()
                to_close = []

                # 第一阶段：在锁内快速收集需要清理的流
                with self.lock:
                    for rtsp_url, stream_info in list(self.streams.items()):
                        if current_time - stream_info['last_used'] > self.max_idle_time:
                            to_close.append((rtsp_url, stream_info.copy()))  # 复制stream_info避免并发修改

                # 第二阶段：在锁外关闭流（避免长时间持有锁）
                for rtsp_url, stream_info in to_close:
                    try:
                        # 先关闭流
                        self._close_stream(rtsp_url, stream_info)

                        # 然后从字典中移除
                        with self.lock:
                            if rtsp_url in self.streams:
                                del self.streams[rtsp_url]
                                idle_time = current_time - stream_info['last_used']
                                logger.info(f"关闭空闲视频流连接: {rtsp_url}, 空闲时间: {idle_time:.1f}秒")
                    except Exception as cleanup_error:
                        logger.error(f"清理单个流时出错 {rtsp_url}: {cleanup_error}")

                # 定期强制垃圾回收，帮助释放内存
                if len(to_close) > 0:
                    import gc
                    gc.collect()
                    logger.debug(f"已清理 {len(to_close)} 个空闲连接，执行垃圾回收")

            except Exception as e:
                logger.error(f"清理空闲连接时出错: {e}", exc_info=True)

        logger.info("视频流连接池清理线程已停止")

    def force_cleanup_all_streams(self):
        """强制清理所有视频流连接

        用于内存压力下的紧急清理或系统关闭时的资源释放
        """
        logger.info("开始强制清理所有视频流连接")

        with self.lock:
            streams_to_close = list(self.streams.items())
            self.streams.clear()

        # 在锁外关闭所有流
        for rtsp_url, stream_info in streams_to_close:
            try:
                self._close_stream(rtsp_url, stream_info)
                logger.info(f"强制关闭视频流连接: {rtsp_url}")
            except Exception as e:
                logger.error(f"强制关闭视频流连接时出错 {rtsp_url}: {e}")

        # 强制垃圾回收
        import gc
        gc.collect()
        logger.info(f"已强制清理 {len(streams_to_close)} 个视频流连接")

# ==============================================================================
# 4. 辅助工具和配置类
# ==============================================================================

def retry(max_retries=3, delay=1, backoff=2, exceptions=(Exception,)):
    """重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            mtries, mdelay = max_retries, delay
            while mtries > 0:
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    mtries -= 1
                    if mtries == 0:
                        logger.error(f"函数 {func.__name__} 达到最大重试次数 {max_retries}，放弃重试", exc_info=True)
                        raise
                    
                    logger.warning(f"函数 {func.__name__} 执行失败: {e}，{mdelay}秒后重试，剩余次数: {mtries}")
                    time.sleep(mdelay)
                    mdelay *= backoff
            # 此处代码理论上不会执行到，因为循环退出条件是 mtries==0 或成功返回
            return func(*args, **kwargs) 
        return wrapper
    return decorator

class RobotPoolConfig:
    """机器人水池巡检配置类"""
    def __init__(self, config_obj):
        self.config = config_obj
        self.robot_config = self.config.env.get('robot_water_level', {})
        self.system_config = self.config.env.get('system', {})
        self.storage_config = self.config.env.get('storage', {})
        
        # 图像捕获配置
        self.base_capture_interval = self.robot_config.get('batch_size_times_interval', 10)
        # 新增堰门摄像头抓取间隔
        self.slag_weir_gate_capture_interval = self.robot_config.get('batch_size_times_interval_slag_weir_gate', 10)
        self.robot_status_check_interval = self.robot_config.get('robot_status_interval', 5)
        self.robot_half_circle_time = self.robot_config.get('robot_half_circle_time', 5400)
        
        # 任务间隔配置
        self.task_intervals = {
            'MOVING': {
                'leaf_recognition': self.robot_config.get('robot_task_leaf_interval_motion', 120),
                'water_level': self.robot_config.get('robot_task_water_interval_motion', 120),
                'moss_recognition': self.robot_config.get('robot_task_moss_interval_motion', 120),
            },
            'STOPPED': {
                # 移除撇渣管任务，改为放入独立类别
                'leaf_recognition': self.robot_config.get('robot_task_leaf_interval_stop', 60),
            },
            # 新增堰门任务类别
            'WEIR_GATE': {
                'slag_weir_gate': self.robot_config.get('robot_task_slag_weir_gate_interval', 3600),
                # 新增撇渣管任务（使用独立的配置）
                'slag_outlet': self.robot_config.get('robot_task_slag_interval_stop', 1200),
            },
        }
        self.other_devices_alarm_interval = 5 * 60  # 5分钟
        
        # 撇渣管控制配置
        self.slag_pipe_control_cooldown = self.robot_config.get('slag_pipe_control_cooldown', 30)  # 默认30分钟冷却期
        # 添加撇渣管连续报警阈值配置
        self.slag_pipe_consecutive_alarm_threshold = self.robot_config.get('slag_pipe_consecutive_alarm_threshold', 3)  # 默认连续3次报警视为卡住
        
        # 堰门控制配置
        self.slag_weir_gate_control_cooldown = self.robot_config.get('slag_weir_gate_control_cooldown', 60)  # 默认60分钟冷却期
        
        # 机器人挡位配置（用于青苔识别控制）
        # 不再需要速度阈值配置，因为现在使用挡位判断，挡位1是慢速，大于1需要减速

        # 系统配置
        self.max_workers = self.system_config.get('thread_pool', {}).get('max_workers', 8)
        self.image_buffer_size = self.robot_config.get('buffer_size', 300)
        
        # 图片最大有效期（秒）
        self.max_frame_age = self.robot_config.get('max_frame_age', 3600)  # 默认1小时
        
        # 存储路径
        self.base_dataset_path = Path(self.storage_config.get('paths', {}).get('base_dataset', 'datasets'))
        
        # 夜间模式配置
        self.night_mode_enabled = self.robot_config.get('night_mode_enabled', False)  # 默认不启用
        self.night_start_hour = self.robot_config.get('night_start_hour', 20)  # 默认晚上8点
        self.night_end_hour = self.robot_config.get('night_end_hour', 4)  # 默认早上4点
        self.night_log_interval = self.robot_config.get('night_log_interval', 3600)  # 默认每小时打印一次日志

# ==============================================================================
# 5. 主系统类 (RobotPoolInspectionSystem)
# ==============================================================================

class RobotPoolInspectionSystem:
    """机器人圆形水池巡检视觉系统（健壮版）"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls, config_obj=None):
        if cls._instance is None:
            cls._instance = cls(config_obj or config)
        return cls._instance
    
    def __init__(self, config_obj):
        self.config_manager = RobotPoolConfig(config_obj)
        self.data_publisher = DataPublisher(config_obj.env)
        
        self.robot_status: Dict[str, str] = {}
        self.last_robot_status: Dict[str, str] = {}
        self.status_change_time: Dict[str, datetime.datetime] = {}
        self.task_last_run_time: Dict[str, Dict[str, Optional[datetime.datetime]]] = {}
        
        # 添加机器人圈ID跟踪
        self.robot_cycle_ids: Dict[str, str] = {}
        # 修改saved_images结构，添加cycle_id
        self.saved_images: List[Tuple[datetime.datetime, str, Dict, str]] = []
        
        self.cameras_info: Dict[str, Dict] = {}
        self.robot_cameras_mapping: Dict[str, Dict] = {}
        self.device_last_alarm_time: Dict[str, datetime.datetime] = {}
        
        # 添加撇渣管控制相关变量
        self.slag_pipe_status: Dict[str, Dict] = {}  # 存储每个机器人的撇渣管状态
        self.last_slag_pipe_control_time: Dict[str, datetime.datetime] = {}  # 存储每个机器人的上次撇渣管控制时间
        # 添加撇渣管连续报警计数器
        self.slag_pipe_consecutive_alarms: Dict[str, int] = {}  # 存储每个机器人的撇渣管连续报警次数
        
        # 添加堰门控制相关变量
        self.slag_weir_gate_status: Dict[str, Dict] = {}  # 存储每个机器人的堰门状态
        self.last_slag_weir_gate_control_time: Dict[str, datetime.datetime] = {}  # 存储每个机器人的上次堰门控制时间
        
        # 添加夜间模式日志控制变量
        self.last_night_mode_log_time: Optional[datetime.datetime] = None
        
        self.executor = ThreadPoolExecutor(max_workers=self.config_manager.max_workers)
        self.status_lock = threading.Lock()
        
        # 关键组件初始化
        self.video_stream_pool: Optional[VideoStreamPool] = None
        
        self.running = False
        self.main_thread: Optional[threading.Thread] = None
        self.event = threading.Event()
        
        logger.info("机器人圆形水池巡检视觉系统初始化完成")

    def start(self):
        """启动巡检系统"""
        if self.running:
            logger.warning("巡检系统已在运行中")
            return
            
        logger.info("启动机器人圆形水池巡检视觉系统")
        self.running = True
        
        # 启动视频流连接池
        self.video_stream_pool = VideoStreamPool()
        self.video_stream_pool.start()
        
        self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.main_thread.start()
        
    def stop(self):
        """停止巡检系统"""
        if not self.running:
            logger.warning("巡检系统已经停止")
            return
            
        logger.info("停止机器人圆形水池巡检视觉系统")
        self.running = False
        self.event.set() # 唤醒可能在等待的主循环
        
        if self.main_thread and self.main_thread.is_alive():
            self.main_thread.join(timeout=5.0)
        
        if self.video_stream_pool:
            self.video_stream_pool.stop()
            
        self.executor.shutdown(wait=True)
        logger.info("机器人圆形水池巡检视觉系统已停止")

    def is_running(self):
        return self.running and (self.main_thread is not None and self.main_thread.is_alive())

    def _main_loop(self):
        """主循环，使用事件驱动模型"""
        logger.info("主循环开始运行")
        
        # 初始化计时器
        next_status_check = datetime.datetime.now()
        next_camera_update = datetime.datetime.now()
        # 使用独立的抓取计时器
        next_patrol_capture = datetime.datetime.now()
        next_weir_gate_capture = datetime.datetime.now()

        # 初始时先更新一次信息
        self._update_cameras_info()
        self._check_robot_status()

        while self.running:
            try:
                # 计算最近的下一个任务时间
                now = datetime.datetime.now()
                next_event_time = min(next_status_check, next_camera_update, next_patrol_capture, next_weir_gate_capture)
                
                wait_time = (next_event_time - now).total_seconds()
                if wait_time > 0:
                    self.event.wait(timeout=wait_time)
                    self.event.clear()
                
                if not self.running: break
                
                now = datetime.datetime.now() # 重新获取唤醒后的时间

                # 执行到期的任务
                if now >= next_status_check:
                    self._check_robot_status()
                    next_status_check = now + datetime.timedelta(seconds=self.config_manager.robot_status_check_interval)

                if now >= next_camera_update:
                    self._update_cameras_info()
                    next_camera_update = now + datetime.timedelta(seconds=self.config_manager.robot_status_check_interval)

                # 分别处理不同类型的摄像头抓取
                if now >= next_patrol_capture:
                    # 遍历所有机器人，只有在运动状态时才抓取其巡检摄像头的帧
                    moving_robots = 0
                    for robot_id, device_data in self.robot_cameras_mapping.items():
                        robot_status = self.robot_status.get(robot_id)
                        if robot_status == "MOVING":
                            moving_robots += 1
                            for camera_config in device_data.get('patrol_cameras', []):
                                camera_id = camera_config.get('camera_id')
                                if camera_id:
                                    self._capture_and_save_frame(camera_id)
                    next_patrol_capture = now + datetime.timedelta(seconds=self.config_manager.base_capture_interval)
                    logger.info(f"已完成巡检摄像头帧抓取，运动中机器人数量: {moving_robots}，下次抓取时间: {next_patrol_capture}")

                if now >= next_weir_gate_capture:
                    # 遍历所有机器人，抓取其堰门摄像头的帧（堰门摄像头独立于机器人状态）
                    total_weir_cameras = 0
                    for robot_id, device_data in self.robot_cameras_mapping.items():
                        for camera_config in device_data.get('weir_gate_cameras', []):
                            camera_id = camera_config.get('camera_id')
                            if camera_id:
                                total_weir_cameras += 1
                                self._capture_and_save_frame(camera_id)
                    next_weir_gate_capture = now + datetime.timedelta(seconds=self.config_manager.slag_weir_gate_capture_interval)
                    logger.info(f"已完成堰门摄像头帧抓取，堰门摄像头数量: {total_weir_cameras}，下次抓取时间: {next_weir_gate_capture}")

                self._schedule_tasks(now)
                
            except Exception as e:
                logger.error(f"主循环发生严重错误: {e}", exc_info=True)
                time.sleep(5) # 发生严重错误时，等待一段时间再继续
                
        logger.info("主循环已结束")
    
    # --- 图像捕获 ---
    @retry(max_retries=2, delay=2)
    def _capture_and_save_frame(self, camera_id: str):
        camera_info = self.cameras_info.get(camera_id)
        if not camera_info:
            logger.warning(f"捕获帧失败: 未找到摄像头 {camera_id} 的信息")
            return None, False

        # 获取RTSP URL
        video_path = camera_info.get("video_path")
        rtsp_url = self._get_rtsp_url(video_path)
        
        # 输出详细的摄像头信息，帮助调试
        logger.info(f"摄像头 {camera_id} 信息: video_path={video_path}, 解析后的rtsp_url={rtsp_url}")
        
        if not rtsp_url:
            logger.warning(f"摄像头 {camera_id} 没有有效的RTSP URL")
            return None, False

        # 从连接池获取抓取器并读取最新帧
        frame = self._capture_frame_with_grabber(rtsp_url)
        if frame is None:
            logger.error(f"无法从摄像头 {camera_id} ({rtsp_url}) 获取视频帧")
            return None, False
        
        current_time = datetime.datetime.now()
        date_dir = current_time.strftime('%Y/%m/%d')
        save_dir = self.config_manager.base_dataset_path / date_dir
        save_dir.mkdir(parents=True, exist_ok=True)
        
        file_name = f"robot_pool_{camera_id}_{current_time.strftime('%Y%m%d_%H%M%S')}.jpg"
        file_path = str(save_dir / file_name)
        
        # 保存图像
        success = cv2.imwrite(file_path, frame)
        if not success:
            logger.error(f"保存图像失败: {file_path}")
            return None, False

        robot_state = self.robot_status.copy()
        try:
            insert_image_metadata(file_path, current_time, json.dumps(robot_state))
        except Exception as db_error:
            logger.error(f"数据库操作(insert_image_metadata)出错: {db_error}")

        # 获取与摄像头关联的机器人ID
        robot_id = self._find_robot_by_camera(camera_id)
        # 获取当前机器人的圈ID，如果没有则使用默认值
        cycle_id = self.robot_cycle_ids.get(robot_id, "default_cycle") if robot_id else "unknown_cycle"
        
        with self.status_lock:
            # 添加cycle_id到保存的图像信息中
            self.saved_images.append((current_time, file_path, robot_state, cycle_id))
            if len(self.saved_images) > self.config_manager.image_buffer_size:
                self.saved_images.pop(0)
        
        logger.info(f"已捕获并保存图像: {file_path}, 圈ID: {cycle_id}")
        return file_path, True
        
    def _capture_frame_with_grabber(self, rtsp_url: str) -> Optional[Any]:
        """从视频流抓取器获取帧数据

        内存优化：使用copy_frame=False避免不必要的内存复制，
        因为帧数据会立即被保存到文件，不需要长期持有
        """
        if not self.video_stream_pool:
            logger.error("视频流连接池未初始化")
            return None
        grabber = self.video_stream_pool.get_stream(rtsp_url)
        if not grabber:
            logger.error(f"无法从连接池获取抓取器: {rtsp_url}")
            return None

        # 内存优化：使用copy_frame=False避免内存复制
        # 因为帧数据会立即被使用（保存到文件），不需要复制
        ret, frame = grabber.read(copy_frame=False)
        return frame if ret else None
    
    def _get_rtsp_url(self, video_path: Any) -> Optional[str]:
        if isinstance(video_path, str) and video_path.startswith("rtsp://"):
            return video_path
        if isinstance(video_path, list):
            for path in video_path:
                if isinstance(path, str) and path.startswith("rtsp://"):
                    return path
        if isinstance(video_path, dict) and "url" in video_path:
            url = video_path["url"]
            if isinstance(url, str) and url.startswith("rtsp://"):
                return url
        logger.warning(f"无法从 {type(video_path)} 类型的 video_path 中提取RTSP URL")
        return None
    
    # --- 状态检查与任务调度 ---
    def _update_cameras_info(self):
        try:
            robot_cameras_mapping = get_robot_and_cameras_status()
            if not robot_cameras_mapping:
                logger.warning("未获取到机器人和摄像头绑定信息")
                self.robot_cameras_mapping, self.cameras_info = {}, {}
                return

            self.robot_cameras_mapping = robot_cameras_mapping
            all_cameras = {}
            for robot_id, device_data in robot_cameras_mapping.items():
                # 合并所有类型的摄像头到一个平面字典中
                for camera_config in device_data.get('patrol_cameras', []):
                    if 'camera_id' in camera_config:
                        all_cameras[camera_config['camera_id']] = camera_config
                for camera_config in device_data.get('weir_gate_cameras', []):
                    if 'camera_id' in camera_config:
                        all_cameras[camera_config['camera_id']] = camera_config
            self.cameras_info = all_cameras
            
            # 记录各类型摄像头的数量
            patrol_count = sum(len(data.get('patrol_cameras', [])) for data in robot_cameras_mapping.values())
            weir_gate_count = sum(len(data.get('weir_gate_cameras', [])) for data in robot_cameras_mapping.values())
            
            logger.info(f"已更新 {len(self.robot_cameras_mapping)} 个机器人的信息，共 {len(self.cameras_info)} 个摄像头 "
                       f"(巡检摄像头: {patrol_count}, 堰门摄像头: {weir_gate_count})")

        except Exception as e:
            logger.error(f"获取机器人和摄像头信息出错: {e}", exc_info=True)

    def _check_robot_status(self):
        for robot_id in list(self.robot_cameras_mapping.keys()):
            try:
                status_info = get_robot_status_info(robot_id)
                if not status_info:
                    logger.warning(f"获取机器人 {robot_id} 状态失败")
                    continue
                
                new_status = "MOVING" if status_info.get("running_status") == 1 else "STOPPED"
                # 测试
                # new_status = "MOVING"
                with self.status_lock:
                    current_status = self.robot_status.get(robot_id)
                    if new_status != current_status:
                        logger.info(f"机器人 {robot_id} 状态从 {current_status} 变为 {new_status}")
                        if new_status == "MOVING":
                            logger.info(f"机器人 {robot_id} 开始运动，将开始图像抓取和识别任务")
                        elif new_status == "STOPPED":
                            logger.info(f"机器人 {robot_id} 停止运动，将停止图像抓取和识别任务")
                        self.robot_status[robot_id] = new_status
                        self.status_change_time[robot_id] = datetime.datetime.now()
                        
                        # 如果状态从STOPPED变为MOVING，生成新的圈ID
                        if new_status == "MOVING" and (current_status is None or current_status == "STOPPED"):
                            new_cycle_id = str(uuid.uuid4())
                            self.robot_cycle_ids[robot_id] = new_cycle_id
                            logger.info(f"机器人 {robot_id} 开始新的一圈，圈ID: {new_cycle_id}")
                        
                        # 保留撇渣管和排渣堰门任务的计时，重置其他任务计时器
                        slag_outlet_time = self.task_last_run_time.get(robot_id, {}).get("slag_outlet")
                        slag_weir_gate_time = self.task_last_run_time.get(robot_id, {}).get("slag_weir_gate")
                        
                        self.task_last_run_time[robot_id] = {
                            "leaf_recognition": None, 
                            "water_level": None, 
                            "other_devices_alarm": None,
                            "moss_recognition": None,
                            # 保留这两个任务的上次执行时间
                            "slag_outlet": slag_outlet_time,
                            "slag_weir_gate": slag_weir_gate_time
                        }
                    # 确保即使状态未变，也有初始值
                    if robot_id not in self.robot_status:
                        self.robot_status[robot_id] = new_status
                        
                        # 保留撇渣管和排渣堰门任务的计时，初始化其他任务计时器
                        slag_outlet_time = self.task_last_run_time.get(robot_id, {}).get("slag_outlet")
                        slag_weir_gate_time = self.task_last_run_time.get(robot_id, {}).get("slag_weir_gate")
                        
                        self.task_last_run_time[robot_id] = {
                            "leaf_recognition": None, 
                            "water_level": None, 
                            "other_devices_alarm": None,
                            "moss_recognition": None,
                            # 保留这两个任务的上次执行时间，如果没有则为None
                            "slag_outlet": slag_outlet_time,
                            "slag_weir_gate": slag_weir_gate_time
                        }
                        
                        # 如果是MOVING状态且没有圈ID，生成新的圈ID
                        if new_status == "MOVING" and robot_id not in self.robot_cycle_ids:
                            new_cycle_id = str(uuid.uuid4())
                            self.robot_cycle_ids[robot_id] = new_cycle_id
                            logger.info(f"机器人 {robot_id} 初始化圈ID: {new_cycle_id}")
                
                # 处理撇渣管状态信息
                if "slag_pipe" in status_info:
                    slag_pipe_data = status_info["slag_pipe"]
                    # 记录撇渣管状态
                    with self.status_lock:
                        # 确保slag_pipe_status字典已初始化
                        if not hasattr(self, 'slag_pipe_status'):
                            self.slag_pipe_status = {}
                        self.slag_pipe_status[robot_id] = slag_pipe_data
                        logger.info(f"更新机器人 {robot_id} 的撇渣管状态: {slag_pipe_data}")
                
                # 处理堰门状态信息
                if "slag_weir_gate" in status_info:
                    slag_weir_gate_data = status_info["slag_weir_gate"]
                    # 记录堰门状态
                    with self.status_lock:
                        # 确保slag_weir_gate_status字典已初始化
                        if not hasattr(self, 'slag_weir_gate_status'):
                            self.slag_weir_gate_status = {}
                        self.slag_weir_gate_status[robot_id] = slag_weir_gate_data
                        logger.info(f"更新机器人 {robot_id} 的堰门状态: {slag_weir_gate_data}")

            except Exception as e:
                logger.error(f"检查机器人 {robot_id} 状态时出错: {e}", exc_info=True)

    def _schedule_tasks(self, current_time: datetime.datetime):
        with self.status_lock:
            # 创建一份副本进行遍历，避免在循环中修改
            robots_to_schedule = list(self.robot_status.keys())
            
        # 检查是否处于夜间模式
        if self._is_night_mode(log_message=True):
            return  # 如果是夜间模式，直接返回，不执行任何任务

        for robot_id in robots_to_schedule:
            with self.status_lock:
                status = self.robot_status.get(robot_id)
                if not status: continue
                
                # 确保 task_times 存在并初始化
                if robot_id not in self.task_last_run_time:
                    self.task_last_run_time[robot_id] = {
                        # "leaf_recognition": None, 
                        "slag_outlet": None,
                        # "water_level": None, 
                        # "other_devices_alarm": None,
                        "moss_recognition": None, 
                        "slag_weir_gate": None
                    }
                task_times = self.task_last_run_time.get(robot_id, {})
                
                # 确保所有任务类型都存在于task_times中
                if "moss_recognition" not in task_times:
                    task_times["moss_recognition"] = None
                if "slag_weir_gate" not in task_times:
                    task_times["slag_weir_gate"] = None
                if "slag_outlet" not in task_times:
                    task_times["slag_outlet"] = None

            # 调度基于状态的任务
            intervals = self.config_manager.task_intervals.get(status, {})
            for task_name, interval in intervals.items():
                # 只处理青苔识别任务 - 移除撇渣管识别
                if task_name not in ["moss_recognition"]:
                    continue
                
                last_run = task_times.get(task_name)
                if last_run is None or (current_time - last_run).total_seconds() >= interval:
                    self._submit_task(task_name, robot_id)
                    with self.status_lock:
                        self.task_last_run_time[robot_id][task_name] = current_time
            
            # 调度堰门和撇渣管任务（不依赖机器人状态）
            weir_gate_intervals = self.config_manager.task_intervals.get('WEIR_GATE', {})
            for task_name, interval in weir_gate_intervals.items():
                last_run = task_times.get(task_name)
                if last_run is None or (current_time - last_run).total_seconds() >= interval:
                    self._submit_task(task_name, robot_id)
                    with self.status_lock:
                        self.task_last_run_time[robot_id][task_name] = current_time
            
            # 注释掉通用任务的调度 (如设备报警)
            # last_alarm_check = task_times.get("other_devices_alarm")
            # alarm_interval = self.config_manager.other_devices_alarm_interval
            # if last_alarm_check is None or (current_time - last_alarm_check).total_seconds() >= alarm_interval:
            #     self._submit_task("other_devices_alarm", robot_id)
            #     with self.status_lock:
            #         self.task_last_run_time.setdefault(robot_id, {})['other_devices_alarm'] = current_time

    def _submit_task(self, task_name: str, robot_id: str):
        # 添加时间间隔日志
        last_run = self.task_last_run_time.get(robot_id, {}).get(task_name)
        if last_run:
            interval = (datetime.datetime.now() - last_run).total_seconds()
            logger.info(f"任务 {task_name} for robot {robot_id} 距离上次执行已过 {interval:.1f} 秒")
        
        if task_name in ["slag_weir_gate", "slag_outlet"]:
            # 获取该机器人绑定的堰门摄像头 - 撇渣管识别也使用堰门摄像头
            weir_cameras = self.robot_cameras_mapping.get(robot_id, {}).get('weir_gate_cameras', [])
            if not weir_cameras:
                logger.warning(f"机器人 {robot_id} 没有绑定堰门摄像头，无法执行任务 {task_name}")
                return
            
            # 注意: 当前实现假设每个机器人只有一个堰门摄像头
            # 如果未来一个机器人可能绑定多个堰门摄像头，这里需要修改为循环处理每个摄像头
            # TODO: 如果未来支持多个堰门摄像头，将此处修改为循环
            camera_id = weir_cameras[0]['camera_id']
            frame_path = self._get_latest_frame_path_for_camera(camera_id)
            if frame_path:
                logger.info(f"提交任务 '{task_name}' for robot {robot_id} with image {frame_path}")
                self.executor.submit(getattr(self, f'_execute_{task_name}'), frame_path, camera_id, robot_id)
            return

        # 获取该机器人绑定的第一个巡检摄像头作为参考
        camera_id = self._get_first_camera_for_robot(robot_id)
        if not camera_id:
            logger.warning(f"机器人 {robot_id} 没有绑定巡检摄像头，无法为任务 {task_name} 找到图像")
            return

        # 只保留青苔识别任务 - 移除撇渣管识别任务
        if task_name in ["moss_recognition"]:
            frame_path = self._get_latest_frame_path_for_camera(camera_id)
            if frame_path:
                logger.info(f"提交任务 '{task_name}' for robot {robot_id} with image {frame_path}")
                self.executor.submit(getattr(self, f'_execute_{task_name}'), frame_path, camera_id, robot_id)
        # 注释掉其他任务的提交代码
        # elif task_name == "leaf_recognition":
        #     frame_path = self._get_latest_frame_path_for_camera(camera_id)
        #     if frame_path:
        #         logger.info(f"提交任务 '{task_name}' for robot {robot_id} with image {frame_path}")
        #         self.executor.submit(self._execute_leaf_recognition, frame_path, camera_id, robot_id)
        # elif task_name == "water_level":
        #     logger.info(f"提交任务 'water_level_comparison' for robot {robot_id}")
        #     self.executor.submit(self._execute_water_level_comparison, camera_id, robot_id)
        # elif task_name == "other_devices_alarm":
        #     logger.info(f"提交任务 'other_devices_alarm' for robot {robot_id}")
        #     self.executor.submit(self._process_other_devices_alarm, robot_id)

    # --- 任务执行 ---
    # @retry(max_retries=2, delay=2)
    # def _execute_leaf_recognition(self, frame_path, camera_id, robot_id):
    #     return self._analyze_image(frame_path, 'system_prompt_leaf_recognition', "树叶识别", camera_id, robot_id)
    
    @retry(max_retries=2, delay=2)
    def _execute_moss_recognition(self, frame_path, camera_id, robot_id):
        # 检查是否处于夜间模式
        if self._is_night_mode(log_message=False):
            night_start = self.config_manager.night_start_hour
            night_end = self.config_manager.night_end_hour
            logger.info(f"当前处于夜间模式 ({night_start}:00-{night_end}:00)，跳过青苔识别任务")
            return {'状态': '夜间模式', '消息': f'当前处于夜间模式 ({night_start}:00-{night_end}:00)，已跳过识别和控制'}
        
        # 检查机器人是否处于刹车状态
        try:
            from server.remote.device import get_robot_braking_status
            braking_status = get_robot_braking_status(robot_id)
            if braking_status and braking_status.get("code") == 200 and braking_status.get("data") == 1:
                logger.info(f"机器人 {robot_id} 正在刹车中，跳过青苔识别任务")
                return {'状态': '刹车中', '消息': '机器人正在刹车中，已跳过识别和控制'}
        except Exception as e:
            logger.warning(f"获取机器人 {robot_id} 刹车状态时出错: {e}")
            # 继续执行任务，不因为获取刹车状态失败而中断流程
        
        # 分析图像获取结果
        result = self._analyze_image(frame_path, 'system_prompt_moss', "青苔识别", camera_id, robot_id)
        
        # 初始化控制状态
        if isinstance(result, dict):
            # 设置默认的控制状态
            result['控制状态'] = "无需控制"
            
            is_moss = result.get('是否为青苔', '否') == '是'
            need_alarm = result.get('是否需要报警', '否') == '是'
            # 测试
            # is_moss = True
            # need_alarm = True
            # 如果检测到青苔并需要报警，考虑控制机器人挡位
            if is_moss and need_alarm:
                # 获取机器人当前挡位信息
                robot_status = get_robot_status_info(robot_id)
                if robot_status and "gear" in robot_status:
                    current_gear = robot_status.get("gear", 1)  # 默认为1档
                    
                    # 判断是否需要控制机器人速度：只有当挡位大于1时才需要减速
                    if current_gear > 1:
                        logger.info(f"检测到严重青苔，当前机器人挡位为 {current_gear}，需要切换到1档慢速挡位，准备控制机器人减速")
                        try:
                            # 调用机器人控制函数并检查返回值
                            control_result = control_robot(device_id=robot_id)
                            if control_result and control_result.get('code') == 200:
                                logger.info(f"机器人减速控制成功: 机器人ID={robot_id}, 操作=减速清理青苔, 当前挡位={current_gear}, 目标挡位=1, 响应消息={control_result.get('msg', '无消息')}")
                                # 添加控制信息到结果中，用于后续数据库记录
                                result['控制状态'] = "已启动机器人减速清洁模式"
                            elif control_result and control_result.get('code') == 403:
                                logger.warning(f"控制机器人 {robot_id} 减速失败: AI控制未开启")
                                result['控制状态'] = "由于控制状态优先级问题,AI控制未开启"
                            else:
                                error_msg = control_result.get('msg', '未知错误') if control_result else '控制失败'
                                logger.warning(f"控制机器人 {robot_id} 减速失败: {error_msg}")
                                result['控制状态'] = f"控制机器人失败: {error_msg}"
                        except Exception as e:
                            logger.error(f"控制机器人 {robot_id} 减速时出错: {e}", exc_info=True)
                            result['控制状态'] = "控制机器人失败"
                    else:
                        logger.info(f"检测到严重青苔，但当前机器人挡位已是 {current_gear} 档(慢速挡位)，无需再次控制")
                        result['控制状态'] = "机器人已在低速清扫运行中"
                else:
                    logger.warning(f"无法获取机器人 {robot_id} 的挡位信息")
                    result['控制状态'] = "无法获取机器人挡位信息"
            # logger.info(f"机器人青苔识别控制状态1: {result.get('控制状态')}")
            
            # 在设置完控制状态后，发布结果到数据库
            self.executor.submit(
                self._publish_result_to_database,
                camera_id,
                result,
                "青苔识别",
                frame_path,
                robot_id
            )
            
        return result
    
    @retry(max_retries=2, delay=2)
    def _execute_slag_outlet(self, frame_path, camera_id, robot_id):
        """执行撇渣管识别任务，使用YOLO模型代替大模型

        线程安全说明：
        - 每次调用都创建独立的YoloOBBRegionDetector实例，确保线程安全
        - 遵循Ultralytics官方文档建议，避免共享模型实例导致的竞态条件
        - 在任务完成后自动释放模型资源

        Args:
            frame_path: 图像路径
            camera_id: 摄像头ID
            robot_id: 机器人ID

        Returns:
            dict: 分析结果
        """
        # 检查是否处于夜间模式
        if self._is_night_mode(log_message=False):
            night_start = self.config_manager.night_start_hour
            night_end = self.config_manager.night_end_hour
            logger.info(f"当前处于夜间模式 ({night_start}:00-{night_end}:00)，跳过撇渣管识别任务")
            return {'状态': '夜间模式', '消息': f'当前处于夜间模式 ({night_start}:00-{night_end}:00)，已跳过识别和控制'}

        # ------------------------------------ YOLO模型线程安全识别撇渣管部分 ----------------------------------->> #
        logger.info(f"开始执行撇渣管识别任务，使用YOLO模型，机器人ID: {robot_id}, 摄像头ID: {camera_id}")

        # 初始化变量，确保在异常情况下也能正确清理
        detector = None
        detection_result = None

        try:
            # 检查图像文件是否存在
            if not os.path.exists(frame_path):
                logger.error(f"图像文件不存在，无法进行分析: {frame_path}")
                return {'错误': f"图像文件不存在: {frame_path}"}

            # 检查图像是否过期
            current_time = datetime.datetime.now()
            frame_time = self._extract_frame_time(frame_path)
            if frame_time is None or (current_time - frame_time).total_seconds() > self.config_manager.max_frame_age:
                logger.warning(f"图像 {frame_path} 已过期，无法进行分析")
                return {'错误': f"图像已过期: {frame_path}"}

            # 使用YOLO模型进行撇渣管识别 - 从配置文件获取模型路径
            model_path = self.config_manager.config.env.get('yolo_model_piezha', 'llms/models/yolo/best-piezha.pt')

            logger.info(f"使用YOLO模型路径: {model_path}")

            # 自定义区域设置 - 针对2560x1440图像的左下角
            # 区域坐标说明：[(左下角), (右下角), (右上角), (左上角)]
            # 坐标格式：(x, y) - x从左到右(0-2560), y从上到下(0-1440)
            custom_regions = {
                "left-bottom-zone": [(0, 1440), (1024, 1440), (1024, 1100), (0, 1100)],    # 左下角整体区域
            }

            # 线程安全：为每个任务创建独立的检测器实例
            # 这确保了每个线程都有自己的YOLO模型实例，避免竞态条件
            logger.debug(f"为任务创建独立的YOLO检测器实例，线程ID: {threading.current_thread().ident}")
            detector = None
            detection_result = None
            
            try:
                detector = YoloOBBRegionDetector(
                    model_path=model_path,
                    region_points=custom_regions,
                    conf_threshold=0.5,
                    iou_threshold=0.2,
                    max_detections=10
                )

                # 进行图像识别
                # 从配置文件读取是否保存输出图像的设置
                save_slag_output = self.config_manager.config.env.get('yolo_save_result', {}).get('slag_outlet', False)
                logger.debug(f"开始YOLO模型推理，图像路径: {frame_path}")
                detection_result = detector.process_image(
                    image_path=frame_path,
                    save_output=save_slag_output,  # 根据配置决定是否保存输出图像
                    output_path='datasets', # 如果保存的话需要填入保存到的地址
                    # show_regions=True
                )
                logger.debug(f"YOLO模型推理完成，检测结果: {detection_result is not None}")
                
            except Exception as yolo_error:
                logger.error(f"YOLO模型处理出错: {yolo_error}", exc_info=True)
                detection_result = None
            finally:
                # 确保检测器资源被正确释放
                if detector is not None:
                    try:
                        # 如果检测器有清理方法，调用它
                        if hasattr(detector, 'cleanup'):
                            detector.cleanup()
                        elif hasattr(detector, 'close'):
                            detector.close()
                        # 手动删除引用，帮助垃圾回收
                        del detector
                        # 强制垃圾回收，特别是对PyTorch模型
                        import gc
                        gc.collect()
                        logger.debug("YOLO检测器资源已清理")
                    except Exception as cleanup_error:
                        logger.warning(f"清理YOLO检测器时出错: {cleanup_error}")
            
            # 解析识别结果
            if detection_result is None:
                logger.warning(f"YOLO模型未返回有效的识别结果: {frame_path}")
                result = {
                    "是否需要报警": "否",
                    "图片分析结果": "未能检测到有效的撇渣管区域或浮渣堆积情况",
                    "处理建议": "请检查摄像头位置和角度，确保撇渣管区域在视野内"
                }
            else:
                # 检查是否在目标区域内检测到目标
                total_detections = detection_result['total_detections']
                # region_detections = detection_result['region_detections']
                
                # 如果在任何区域内检测到目标，则需要报警
                need_alarm = total_detections > 0
                
                # 构建分析结果
                if need_alarm:
                    
                    result = {
                        "是否需要报警": "是",
                        "图片分析结果": "刮臂已经移动到撇渣管位置。",
                        "处理建议": "正在开启撇渣管进行排渣操作."
                    }
                else:
                    result = {
                        "是否需要报警": "否",
                        "图片分析结果": "撇渣管附近没有看到刮臂。",
                        "处理建议": "继续监控，无需特别处理."
                    }
            # <<------------------------------------ 修改后 YOLO模型 识别撇渣管部分----------------------------------- #
            # 初始化控制状态
            result['控制状态'] = "无需控制"
            # 测试撇渣管
            # result['是否需要报警'] = '是'
            
            # 更新连续报警计数
            with self.status_lock:
                # 如果需要报警，增加计数；否则重置计数
                if result.get('是否需要报警', '否') == '是':
                    # 获取当前计数，如果不存在则初始化为0
                    current_count = self.slag_pipe_consecutive_alarms.get(robot_id, 0)
                    # 增加计数
                    self.slag_pipe_consecutive_alarms[robot_id] = current_count + 1
                    consecutive_alarms = self.slag_pipe_consecutive_alarms[robot_id]
                    logger.info(f"机器人 {robot_id} 的撇渣管连续报警次数: {consecutive_alarms}")
                else:
                    # 如果不需要报警，重置计数
                    if robot_id in self.slag_pipe_consecutive_alarms and self.slag_pipe_consecutive_alarms[robot_id] > 0:
                        logger.info(f"机器人 {robot_id} 的撇渣管报警已恢复正常，重置连续报警计数")
                    self.slag_pipe_consecutive_alarms[robot_id] = 0
                    consecutive_alarms = 0
                
                # 获取配置的连续报警阈值
                alarm_threshold = self.config_manager.slag_pipe_consecutive_alarm_threshold
            
            # 检查是否需要控制撇渣管
            if result.get('是否需要报警', '否') == '是':
                # 添加连续报警信息到结果中
                result['连续报警次数'] = consecutive_alarms
                
                # 检查是否达到连续报警阈值
                if consecutive_alarms >= alarm_threshold:
                    # 已达到阈值，可能是卡住了
                    result['撇渣管状态'] = "可能卡住"
                    logger.warning(f"机器人 {robot_id} 的撇渣管连续报警 {consecutive_alarms} 次，可能卡住")
                
                # 检查是否可以控制撇渣管
                with self.status_lock:
                    # 获取撇渣管状态数据
                    slag_pipe_data = getattr(self, 'slag_pipe_status', {}).get(robot_id, {})
                    
                    # 检查控制条件
                    ai_control_status = slag_pipe_data.get('ai_control_status', 1)  # 默认为1表示能AI控制,因为已经AI控制了
                    remote_control_status = slag_pipe_data.get('remote_control_status', 1)  # 默认为1表示远程控制中
                    on_site_control_status = slag_pipe_data.get('on_site_control_status', 1)  # 默认为1表示现场控制中
                    close_valve_status = slag_pipe_data.get('close_valve_status', 1)  # 默认为1表示已关闭
                    open_valve_status = slag_pipe_data.get('open_valve_status', 1)  # 默认为1表示已打开
                    logger.info(f"撇渣管状态数据: ai_control_status={ai_control_status}, remote_control_status={remote_control_status}, "
                               f"on_site_control_status={on_site_control_status}, close_valve_status={close_valve_status}, "
                               f"open_valve_status={open_valve_status}")
                    # 判断是否满足AI控制条件
                    can_control = (ai_control_status == 1 and 
                                  remote_control_status == 0 and 
                                  on_site_control_status == 0 and
                                  open_valve_status == 0 )
                    logger.info(f"是否满足AI控制条件: {can_control}")
                    # 测试撇渣管
                    # can_control = True
                    # 检查上次控制时间，确保冷却期已过
                    last_control_time = getattr(self, 'last_slag_pipe_control_time', {}).get(robot_id)
                    current_time = datetime.datetime.now()
                    time_elapsed = float('inf')
                    
                    if last_control_time:
                        time_elapsed = (current_time - last_control_time).total_seconds() / 60  # 转换为分钟
                    
                    # 获取配置的冷却时间（分钟）
                    cooldown_minutes = self.config_manager.slag_pipe_control_cooldown
                    
                    # 如果连续报警次数达到阈值，可能是卡住了，需要特殊处理
                    if consecutive_alarms >= alarm_threshold:
                        # 如果可能卡住，记录但不进行控制，等待人工干预
                        result['控制状态'] = f"刮臂可能停机或者卡住，已连续报警{consecutive_alarms}次，建议人工检查"
                        logger.warning(f"刮臂可能停机或者卡住，已连续报警{consecutive_alarms}次，不进行自动控制")
                    # 如果满足控制条件且冷却时间已过，则控制撇渣管
                    elif can_control and time_elapsed >= cooldown_minutes:
                        logger.info(f"检测到浮渣堆积，满足AI控制条件，正在控制机器人 {robot_id} 的撇渣管开启")
                        try:
                            # 调用撇渣管控制函数并检查返回值
                            control_result = control_slag_pipe(device_id=robot_id)
                            if control_result and control_result.get('code') == 200:
                                # 记录控制时间
                                if not hasattr(self, 'last_slag_pipe_control_time'):
                                    self.last_slag_pipe_control_time = {}
                                self.last_slag_pipe_control_time[robot_id] = current_time
                                logger.info(f"撇渣管控制成功: 机器人ID={robot_id}, 操作=开启撇渣管, 响应消息={control_result.get('msg', '无消息')}")
                                result['控制状态'] = "已控制撇渣管开启"
                            elif control_result and control_result.get('code') == 403:
                                logger.warning(f"控制机器人 {robot_id} 的撇渣管开启失败: AI控制未开启")
                                result['控制状态'] = "由于控制状态优先级问题,AI控制未开启"
                            else:
                                error_msg = control_result.get('msg', '未知错误') if control_result else '控制失败'
                                logger.warning(f"控制机器人 {robot_id} 的撇渣管开启失败: {error_msg}")
                                result['控制状态'] = f"控制撇渣管失败: {error_msg}"
                        except Exception as e:
                            logger.error(f"控制机器人 {robot_id} 的撇渣管时出错: {e}", exc_info=True)
                            result['控制状态'] = "控制撇渣管失败"
                    # 暂时不需要冷却时间
                    # elif can_control:
                    #     logger.info(f"检测到浮渣堆积，但距离上次控制仅过去 {time_elapsed:.1f} 分钟，需要等待至少{cooldown_minutes}分钟")
                    #     result['控制状态'] = f"冷却期未过，还需等待{cooldown_minutes-time_elapsed:.1f}分钟"
                    elif can_control:
                        logger.info(f"检测到浮渣堆积，但距离上次控制仅过去 {time_elapsed:.1f} 分钟")
                        result['控制状态'] = "连续时间内识别到多次刮臂,为防止频繁控制,请等待冷却时间"
                    else:
                        logger.info(f"检测到浮渣堆积，但不满足AI控制条件：ai_control_status={ai_control_status}, "
                                   f"remote_control_status={remote_control_status}, "
                                   f"on_site_control_status={on_site_control_status}, "
                                   f"close_valve_status={close_valve_status}")
                        result['控制状态'] = "不满足AI控制条件"
            
            # 在设置完控制状态后，发布结果到数据库
            self.executor.submit(
                self._publish_result_to_database,
                camera_id,
                result,
                "撇渣管识别",
                frame_path,
                robot_id
            )
            
            return result

        except Exception as e:
            logger.error(f"执行撇渣管识别任务时出错: {e}", exc_info=True)
            return {
                "是否需要报警": "否",
                "图片分析结果": f"识别过程中出现错误: {str(e)}",
                "处理建议": "请检查系统日志并联系技术支持",
                "控制状态": "识别失败"
            }

        finally:
            # 线程安全资源清理：确保YOLO模型实例被正确释放
            # 这是防止内存泄漏和double free错误的关键步骤
            if detector is not None:
                try:
                    # 显式清理检测器资源
                    if hasattr(detector, 'model') and detector.model is not None:
                        # 清理YOLO模型资源
                        del detector.model

                    # 清理检测器实例
                    del detector
                    logger.debug(f"已清理YOLO检测器资源，线程ID: {threading.current_thread().ident}")

                except Exception as cleanup_error:
                    # 记录清理错误，但不影响主要流程
                    logger.warning(f"清理YOLO检测器资源时出错: {cleanup_error}")

            # 强制垃圾回收，帮助释放内存
            import gc
            gc.collect()
    
    @retry(max_retries=2, delay=3, backoff=2)
    def _analyze_image(self, frame_path: str, system_type: str, analysis_type: str, camera_id: str, robot_id: str):
        """
        通用的图像分析函数。

        Args:
            frame_path (str): 待分析的图像路径。
            system_type (str): 用于AI模型的系统提示词类型。
            analysis_type (str): 分析任务的名称（如 "树叶识别"），用于日志和结果发布。
            camera_id (str): 产生该图像的摄像头ID。
            robot_id (str): 执行任务的机器人ID。

        Returns:
            dict: AI模型返回的分析结果。
        """
        temp_filepath = None
        
        try:
            logger.info(f"开始执行 '{analysis_type}' 任务 for robot {robot_id} on image: {frame_path}")
            
            # 检查是否处于夜间模式
            if self._is_night_mode(log_message=False):
                night_start = self.config_manager.night_start_hour
                night_end = self.config_manager.night_end_hour
                # 使用debug级别记录具体任务的跳过，避免日志过多
                logger.info(f"当前处于夜间模式 ({night_start}:00-{night_end}:00)，跳过 '{analysis_type}' 任务")
                return {'状态': '夜间模式', '消息': f'当前处于夜间模式 ({night_start}:00-{night_end}:00)，已跳过识别和控制'}

            # 检查图像文件是否存在
            if not os.path.exists(frame_path):
                logger.error(f"图像文件不存在，无法进行分析: {frame_path}")
                return {'错误': f"图像文件不存在: {frame_path}"}
                
            # 检查图像是否过期
            current_time = datetime.datetime.now()
            frame_time = self._extract_frame_time(frame_path)
            if frame_time is None or (current_time - frame_time).total_seconds() > self.config_manager.max_frame_age:
                logger.warning(f"图像 {frame_path} 已过期，无法进行分析")
                return {'错误': f"图像已过期: {frame_path}"}
                
            # 根据任务类型选择对应的坐标文件
            coords_file = None
            if "leaf_recognition" in system_type or analysis_type == "树叶识别":
                coords_file = "llms/utils/split_coords/leaf_coords.txt"
            elif "slag_outletv2" in system_type or analysis_type == "撇渣管识别":
                coords_file = "llms/utils/split_coords/slag_outlet-25601440_coords.txt"
            elif "water_level" in system_type or analysis_type == "水流对比":
                coords_file = "llms/utils/split_coords/moss-25601440_coords.txt"
            elif "moss" in system_type or analysis_type == "青苔识别":
                # 如果没有专门的青苔识别坐标文件，可以使用水位坐标文件
                coords_file = "llms/utils/split_coords/moss-25601440_coords.txt"
            elif "slag_weir_gate" in system_type or analysis_type == "排渣堰门识别":
                # 如果有专门的堰门坐标文件，使用它
                coords_file = "llms/utils/split_coords/slag_weir_gate-25601440_coords.txt"  # 暂时使用撇渣管的坐标文件
            
            # 使用辅助方法预处理图像
            temp_filepath = self._preprocess_image_for_analysis(frame_path, coords_file, analysis_type)
            
            try:
                # 调用大模型API进行分析，使用处理后的图像
                result = process_image(
                    frame=temp_filepath,
                    system_type=system_type
                )
                
                logger.info(f"'{analysis_type}' 任务完成 for robot {robot_id}. 结果: {json.dumps(result, ensure_ascii=False)}")
                
                # 检查返回结果是否是有效的字典
                if isinstance(result, dict):
                    # 删除这里的发布代码，由调用者决定何时发布
                    # self.executor.submit(
                    #     self._publish_result_to_database,
                    #     camera_id,
                    #     result,
                    #     analysis_type,
                    #     frame_path,  # 使用原始图像路径
                    #     robot_id
                    # )
                    return result
                else:
                    logger.warning(f"'{analysis_type}' for robot {robot_id} 返回的结果格式异常: {result}")
                    return {'错误': f"返回结果格式异常: {result}"}
            finally:
                # 清理临时文件
                if temp_filepath != frame_path and os.path.exists(temp_filepath):
                    try:
                        os.remove(temp_filepath)
                        logger.info(f"已删除临时文件: {temp_filepath}")
                    except Exception as e:
                        logger.warning(f"删除临时文件时出错: {e}")
        
        except Exception as e:
            # 异常会被 @retry 装饰器捕获
            logger.error(f"执行 '{analysis_type}' 任务时 for robot {robot_id} 发生严重错误: {e}")
            # 清理临时文件
            if temp_filepath and temp_filepath != frame_path and os.path.exists(temp_filepath):
                try:
                    os.remove(temp_filepath)
                except Exception:
                    pass
            # 重新抛出异常，以便重试机制能够工作
            raise e
    
    # --- 辅助方法 ---
    def _get_first_camera_for_robot(self, robot_id: str) -> Optional[str]:
        """获取机器人绑定的第一个巡检摄像头ID"""
        patrol_cameras = self.robot_cameras_mapping.get(robot_id, {}).get('patrol_cameras', [])
        return patrol_cameras[0]['camera_id'] if patrol_cameras else None
    
    def _extract_camera_id_from_path(self, frame_path: str) -> Optional[str]:
        try:
            file_name = os.path.basename(frame_path)
            parts = file_name.split('_')
            if len(parts) >= 3 and parts[0] == "robot" and parts[1] == "pool":
                return parts[2]
        except Exception: return None
        return None

    def _extract_frame_time(self, frame_path: str) -> Optional[datetime.datetime]:
        """从图片文件名中提取时间戳
        
        文件名格式: robot_pool_camera_id_YYYYMMDD_HHMMSS.jpg
        
        Args:
            frame_path: 图片路径
            
        Returns:
            datetime.datetime: 图片时间戳或None
        """
        try:
            file_name = os.path.basename(frame_path)
            # 去掉扩展名
            file_name = os.path.splitext(file_name)[0]
            parts = file_name.split('_')
            
            # 检查文件名格式是否正确
            if len(parts) >= 5 and parts[0] == "robot" and parts[1] == "pool":
                # 提取日期和时间部分
                date_part = parts[-2]  # YYYYMMDD
                time_part = parts[-1]  # HHMMSS
                
                if len(date_part) == 8 and len(time_part) == 6:
                    # 解析日期时间
                    timestamp_str = f"{date_part}_{time_part}"
                    return datetime.datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
            
            # 如果文件名格式不符合预期，尝试获取文件的修改时间
            return datetime.datetime.fromtimestamp(os.path.getmtime(frame_path))
            
        except Exception as e:
            logger.warning(f"无法从文件名 {frame_path} 提取时间戳: {e}")
            try:
                # 尝试使用文件的修改时间作为备选
                return datetime.datetime.fromtimestamp(os.path.getmtime(frame_path))
            except Exception:
                return None
        
        return None

    def _get_latest_frame_path_for_camera(self, camera_id: str, cycle_id: str = None) -> Optional[str]:
        with self.status_lock:
            current_time = datetime.datetime.now()
            found_camera = False
            
            # 从后往前遍历以更快找到最新的
            for frame_time, frame_path, __, frame_cycle_id in reversed(self.saved_images):
                # 如果指定了cycle_id，则只考虑该圈ID的图像
                if cycle_id and frame_cycle_id != cycle_id:
                    continue
                    
                if self._extract_camera_id_from_path(frame_path) == camera_id:
                    found_camera = True
                    # 检查图片是否过期
                    if (current_time - frame_time).total_seconds() <= self.config_manager.max_frame_age:
                        return frame_path
                    else:
                        age_seconds = (current_time - frame_time).total_seconds()
                        logger.warning(f"摄像头 {camera_id} 的图像 {frame_path} 已过期，时间差: {age_seconds:.1f}秒，继续查找更早的图像")
                        # 不再break，继续检查其他图像
            
            # 如果没有找到有效的图像
            if found_camera:
                if cycle_id:
                    logger.warning(f"摄像头 {camera_id} 在圈ID {cycle_id} 中没有有效图像")
                else:
                    logger.warning(f"摄像头 {camera_id} 的所有图像均已过期，可能已断开连接")
            else:
                if cycle_id:
                    logger.warning(f"摄像头 {camera_id} 在圈ID {cycle_id} 中没有任何图像记录")
                else:
                    logger.warning(f"摄像头 {camera_id} 没有任何图像记录")
            return None
        
    def _find_matching_frame(self, target_time, tolerance_seconds=30, camera_id=None, cycle_id=None):
        """查找与目标时间最接近的图像帧
        
        Args:
            target_time: 目标时间
            tolerance_seconds: 时间容差(秒)
            camera_id: 摄像头ID，如果提供则只查找该摄像头的图像
            cycle_id: 圈ID，如果提供则只查找该圈ID的图像
            
        Returns:
            tuple: (时间戳, 图像路径) 或 None
        """
        try:
            with self.status_lock:
                if not self.saved_images:
                    return None
                
                # 初始化变量
                best_match = None
                min_diff = float('inf')
                current_time = datetime.datetime.now()
                
                # 遍历保存的图像，查找与目标时间最接近的帧
                for timestamp, frame_path, __, frame_cycle_id in self.saved_images:
                    # 如果指定了cycle_id，则只考虑该圈ID的图像
                    if cycle_id is not None and frame_cycle_id != cycle_id:
                        continue
                        
                    # 如果指定了camera_id，则只考虑该摄像头的图像
                    if camera_id is not None:
                        frame_camera_id = self._extract_camera_id_from_path(frame_path)
                        if frame_camera_id != camera_id:
                            continue
                            
                    # 检查图片是否过期
                    if (current_time - timestamp).total_seconds() > self.config_manager.max_frame_age:
                        continue
                        
                    # 计算时间差(秒)
                    time_diff = abs((timestamp - target_time).total_seconds())
                    
                    # 如果时间差小于当前最小差异，更新最佳匹配
                    if time_diff < min_diff:
                        min_diff = time_diff
                        best_match = (timestamp, frame_path)
                
                # 检查是否找到有效的匹配并且时间差是否在容差范围内
                if best_match is not None and min_diff <= tolerance_seconds:
                    return best_match
                else:
                    if best_match is None:
                        if camera_id:
                            cycle_msg = f"圈ID {cycle_id} 中" if cycle_id else ""
                            logger.warning(f"未找到摄像头 {camera_id} {cycle_msg}的有效图像帧，所有图像可能已过期")
                        else:
                            logger.warning(f"未找到有效的图像帧，所有图像可能已过期")
                    else:
                        if camera_id:
                            cycle_msg = f"圈ID {cycle_id} 中" if cycle_id else ""
                            logger.warning(f"未找到摄像头 {camera_id} {cycle_msg}与目标时间 {target_time} 匹配的图像帧，最接近的时间差为 {min_diff} 秒")
                        else:
                            logger.warning(f"未找到与目标时间 {target_time} 匹配的图像帧，最接近的时间差为 {min_diff} 秒")
                    return None
        
        except Exception as e:
            logger.error(f"查找匹配图像帧时出错: {e}")
            return None

    def _publish_result_to_database(self, camera_id, result, analysis_type, frame_path, robot_id=None):
        """发布结果到数据库
        
        Args:
            camera_id: 摄像头ID
            result: 分析结果
            analysis_type: 分析类型
            frame_path: 图像路径
            robot_id: 机器人ID（可选）
        """
        try:
            # 如果没有提供robot_id，尝试查找camera_id对应的robot_id
            if robot_id is None:
                robot_id = self._find_robot_by_camera(camera_id)
                
            # 如果仍然没有robot_id，记录警告并使用camera_id作为fallback
            if robot_id is None:
                logger.warning(f"未能找到摄像头 {camera_id} 对应的机器人ID，将使用摄像头ID作为替代")
                robot_id = f"robot_unknown_{camera_id}"
                
            # 获取摄像头的video_id
            camera_info = self.cameras_info.get(camera_id)
            video_id = camera_info.get("video_id", "") if camera_info else ""
            
            # 准备要发布的数据
            timestamp = datetime.datetime.now()
            frame_number = 0  # 帧号信息未知
            
            # 根据分析类型和结果确定警报状态和异常标志
            alarm_status = "NO_ALARM"
            is_abnormal = False
            failure_reasons_type = []
            coverage_rate = 1
            coverage_level = "LOW"
            do_value =  None   # DO值未知
            mlss_value =  None  # MLSS值未知
            
            # 提取分析详情和建议
            analysis_detail = ""
            adjustment_suggestion = ""
            alarmtype = ""  # 新增字段：报警类型
            
            # 获取控制状态（如果有）
            control_status = result.get('控制状态', '')
            
            if analysis_type == "树叶识别":
                if '是否需要报警' in result:
                    if result.get('是否需要报警', '否') == '是':
                        alarm_status = "WARNING"
                        is_abnormal = True
                        failure_reasons_type = ["树叶堆积过多"]
                        coverage_rate = 99
                        coverage_level = "HIGH"
                        alarmtype = "树叶识别分析,#448ef7"  # 设置报警类型

                analysis_detail = f"分析结果: {result.get('图片分析结果', '未知')}"
                adjustment_suggestion = result.get('处理建议', '无建议')
            
            elif analysis_type == "撇渣管识别":
                # 解析撇渣管识别结果
                if '是否需要报警' in result:
                    if result.get('是否需要报警', '否') == '是':
                        alarm_status = "WARNING"
                        is_abnormal = True
                        failure_reasons_type = ["撇渣管浮渣堆积"]
                        coverage_rate = 99
                        coverage_level = "HIGH"
                        alarmtype = "池面浮渣分析,#448ef7"  # 设置报警类型
                        
                        # 添加连续报警信息到分析详情中
                        consecutive_alarms = result.get('连续报警次数', 0)
                        if consecutive_alarms > 0:
                            # 获取阈值配置
                            alarm_threshold = self.config_manager.slag_pipe_consecutive_alarm_threshold
                            
                            # 如果达到或超过阈值，添加卡住警告
                            if consecutive_alarms >= alarm_threshold:
                                analysis_detail = f"分析结果: {result.get('图片分析结果', '未知')} [警告: 已连续识别到刮臂次数{consecutive_alarms}次，刮臂可能卡住]"
                            else:
                                analysis_detail = f"分析结果: {result.get('图片分析结果', '未知')} [连续识别到刮臂次数: {consecutive_alarms}]"
                        else:
                            analysis_detail = f"分析结果: {result.get('图片分析结果', '未知')}"
                    else:
                        # 不需要报警的情况
                        analysis_detail = f"分析结果: {result.get('图片分析结果', '未知')}"
                else:
                    analysis_detail = f"分析结果: {result.get('图片分析结果', '未知')}"
                
                # 在调整建议中添加控制状态
                if control_status:
                    adjustment_suggestion = f"{result.get('处理建议', '无建议')} 控制状态: {control_status}"
                else:
                    adjustment_suggestion = result.get('处理建议', '无建议')
            
            elif analysis_type == "排渣堰门识别":
                # 解析排渣堰门识别结果
                if '浮渣严重程度' in result:
                    severity = result.get('浮渣严重程度', '基本清洁')
                    if severity in ['严重', '中等']:
                        alarm_status = "WARNING"
                        is_abnormal = True
                        failure_reasons_type = ["排渣堰门浮渣堆积"]
                        coverage_rate = 99
                        coverage_level = "HIGH"
                        alarmtype = "进水槽识别分析,#448ef7"  # 设置报警类型

                # 不在分析详情中添加控制状态
                analysis_detail = f"分析结果: {result.get('图片分析结果', '当前没有识别到排渣堰门附近的浮渣，正在继续观察中...')}"
                
                # 在调整建议中添加控制状态
                if control_status:
                    adjustment_suggestion = f"{result.get('处理建议', '无建议')} 控制状态: {control_status}"
                else:
                    adjustment_suggestion = result.get('处理建议', '无建议')
            
            elif analysis_type == "水流对比":
                # 解析水流对比结果
                water_trend = result.get('出水变化趋势', '无明显变化')
                water_change_detected = water_trend in ['变大', '变小']
                
                if water_change_detected:
                    alarm_status = "WARNING"
                    is_abnormal = True
                    failure_reasons_type = ["排水堰出现水流变化"]
                    coverage_rate = 99
                    coverage_level = "HIGH"
                    alarmtype = "水流对比分析,#448ef7"  # 设置报警类型
                
                analysis_detail = f"变化描述: {result.get('变化描述', '')}"
                adjustment_suggestion = result.get('风险建议', '无建议')
            
            elif analysis_type == "青苔识别":
                # 解析青苔识别结果 - 双重判断：必须同时满足"是否为青苔"为"是"且"是否需要报警"为"是"
                is_moss = result.get('是否为青苔', '否') == '是'
                need_alarm = result.get('是否需要报警', '否') == '是'
                
                if is_moss and need_alarm:
                    alarm_status = "WARNING"
                    is_abnormal = True
                    failure_reasons_type = ["出现青苔"]
                    coverage_rate = 99
                    coverage_level = "HIGH"
                    alarmtype = "青苔识别分析,#448ef7"  # 设置报警类型
                
                # 不在分析详情中添加控制状态
                analysis_detail = f"分析结果: {result.get('图片分析结果', '当前没有识别到青苔，正在继续观察中...')}"
                
                # 根据控制状态设置不同的建议
                if control_status:
                    # logger.info(f"机器人青苔识别控制状态1: {control_status}")
                    if control_status == "已启动机器人减速清洁模式":
                        adjustment_suggestion = f"已识别到青苔，已启动机器人减速清洁模式。控制状态: {control_status}"
                    elif control_status == "机器人已在低速运行中":
                        adjustment_suggestion = f"当前识别到青苔已经开始启动机器人进行清洁，我将继续进行青苔的识别任务。控制状态: {control_status}"
                    else:
                        adjustment_suggestion = f"{result.get('处理建议', '当前没有识别到青苔，正在继续观察。')} 控制状态: {control_status}"
                else:
                    adjustment_suggestion = result.get('处理建议', '当前没有识别到青苔，正在继续观察。')
            # logger.info(f"机器人青苔识别控制状态2: {result.get('控制状态')}")
            logger.info(f"机器人识别控制建议: {adjustment_suggestion}")
            
            # 检查并设置合理的video_id
            if not video_id and camera_info:
                # 如果没有video_id但有camera_info，使用e9_code
                video_id = camera_info.get("e9_code", "")
            
            # 如果都没有，使用camera_id作为video_id
            if not video_id:
                video_id = f"cam_{camera_id}"
            
            # 准备发布的数据 - 使用robot_id代替camera_id
            frame_data = (
                robot_id,                    # robot_id (替换原来的camera_id)
                video_id,                    # video_id
                frame_number,                # frame_number
                timestamp,                   # timestamp
                frame_path,                  # frame_path
                coverage_rate,               # coverage_rate
                coverage_level,              # coverage_level
                alarm_status,                # alarm_status
                analysis_detail,             # analysis_detail
                is_abnormal,                 # is_abnormal
                do_value,                    # do_value
                mlss_value,                  # mlss_value
                adjustment_suggestion,       # adjustment_suggestion
                failure_reasons_type,        # failure_reasons_type
                # failure_reasons_number,      # failure_reasons_number
                alarmtype                    # alarmtype
            )
            
            # 发布数据到数据库
            logger.info(f"发布机器人 {robot_id} 的 {analysis_type} 分析结果到数据库 (摄像头: {camera_id}), 控制状态: {control_status}")
            self.data_publisher.publish_data(frame_data)
            
        except Exception as e:
            logger.error(f"发布结果到数据库时出错: {e}", exc_info=True)

    def _find_robot_by_camera(self, camera_id: str) -> Optional[str]:
        """根据摄像头ID查找对应的机器人ID
        
        Args:
            camera_id: 摄像头ID
            
        Returns:
            str: 机器人ID或None
        """
        for robot_id, device_data in self.robot_cameras_mapping.items():
            # 检查巡检摄像头
            for camera_config in device_data.get('patrol_cameras', []):
                if camera_config.get('camera_id') == camera_id:
                    return robot_id
            # 检查堰门摄像头
            for camera_config in device_data.get('weir_gate_cameras', []):
                if camera_config.get('camera_id') == camera_id:
                    return robot_id
        return None

    def _is_pool_robot_camera(self, camera_info: Dict) -> bool:
        """检查摄像头是否是圆形水池巡检机器人摄像头
        
        Args:
            camera_info: 摄像头信息
            
        Returns:
            bool: 是否是圆形水池巡检机器人摄像头
        """
        # 根据实际需求实现检查逻辑
        # 例如检查摄像头类型、名称或其他属性
        camera_type = camera_info.get('camera_type', '')
        camera_name = camera_info.get('camera_name', '')
        
        # 示例逻辑，根据实际情况调整
        return ('pool' in camera_type.lower() or 
                'pool' in camera_name.lower() or 
                '水池' in camera_name)

    @retry(max_retries=2, delay=2)
    def _execute_slag_weir_gate(self, frame_path, camera_id, robot_id):
        """执行排渣堰门识别任务
        
        Args:
            frame_path: 图像路径
            camera_id: 摄像头ID
            robot_id: 机器人ID
            
        Returns:
            dict: 分析结果
        """
        # 检查是否处于夜间模式
        if self._is_night_mode(log_message=False):
            night_start = self.config_manager.night_start_hour
            night_end = self.config_manager.night_end_hour
            logger.info(f"当前处于夜间模式 ({night_start}:00-{night_end}:00)，跳过排渣堰门识别任务")
            return {'状态': '夜间模式', '消息': f'当前处于夜间模式 ({night_start}:00-{night_end}:00)，已跳过识别和控制'}
        
        logger.info(f"开始执行排渣堰门识别任务，机器人ID: {robot_id}, 摄像头ID: {camera_id}")
        
        # 分析图像获取结果
        result = self._analyze_image(frame_path, 'system_prompt_slag_weir_gate', "排渣堰门识别", camera_id, robot_id)
        
        # 检查是否需要控制堰门
        if result.get('浮渣严重程度', '基本清洁') in ['严重']:
            # 控制堰门打开
            whether_control_slag_weir_gate = True
        else:
            whether_control_slag_weir_gate = False
        # 测试
        # whether_control_slag_weir_gate = True
        if whether_control_slag_weir_gate:
            logger.info(f"检测到堰门需要控制，机器人ID: {robot_id}")
            
            # 检查上次控制时间，确保冷却期已过
            last_control_time = getattr(self, 'last_slag_weir_gate_control_time', {}).get(robot_id)
            current_time = datetime.datetime.now()
            time_elapsed = float('inf')
            
            if last_control_time:
                time_elapsed = (current_time - last_control_time).total_seconds() / 60  # 转换为分钟
            
            # 获取配置的冷却时间（分钟）
            # cooldown_minutes = self.config_manager.slag_weir_gate_control_cooldown
            cooldown_minutes = 0
            # 暂时不需要冷却时间
            # 如果冷却时间已过，则检查堰门状态并控制
            if time_elapsed >= cooldown_minutes:
                # 获取堰门状态数据
                with self.status_lock:
                    slag_weir_gate_data = getattr(self, 'slag_weir_gate_status', {}).get(robot_id, {})
                    
                # 检查堰门控制条件
                ai_control_status = slag_weir_gate_data.get('ai_control_status', 1)  # 默认为1表示能AI控制
                remote_control_status = slag_weir_gate_data.get('remote_control_status', 1)  # 默认为1表示远程控制中
                on_site_control_status = slag_weir_gate_data.get('on_site_control_status', 1)  # 默认为1表示现场控制中
                close_gate_status = slag_weir_gate_data.get('close_gate_status', 1)  # 默认为1表示已关闭
                open_gate_status = slag_weir_gate_data.get('open_gate_status', 1)  # 默认为1表示已打开
                
                logger.info(f"堰门状态数据: ai_control_status={ai_control_status}, remote_control_status={remote_control_status}, "
                           f"on_site_control_status={on_site_control_status}, close_status={close_gate_status}, "
                           f"open_status={open_gate_status}")
                
                # 判断是否满足AI控制条件
                can_control = (ai_control_status == 1 and 
                              remote_control_status == 0 and 
                              on_site_control_status == 0 and
                              open_gate_status == 0)
                
                logger.info(f"堰门是否满足AI控制条件: {can_control}")
                
                # 测试用
                # can_control = True
                
                if can_control:
                    try:
                        # 调用堰门控制函数并检查返回值
                        control_result = control_slag_weir_gate(device_id=robot_id)
                        if control_result and control_result.get('code') == 200:
                            # 记录控制时间
                            if not hasattr(self, 'last_slag_weir_gate_control_time'):
                                self.last_slag_weir_gate_control_time = {}
                            self.last_slag_weir_gate_control_time[robot_id] = current_time
                            
                            logger.info(f"堰门控制成功: 机器人ID={robot_id}, 操作=开启排渣堰门, 浮渣严重程度={result.get('浮渣严重程度', '未知')}, 响应消息={control_result.get('msg', '无消息')}")
                            # 添加控制信息到结果中，用于后续数据库记录
                            result['控制状态'] = "已控制堰门"
                        elif control_result and control_result.get('code') == 403:
                            logger.warning(f"控制机器人 {robot_id} 的堰门失败: AI控制未开启")
                            result['控制状态'] = "由于控制状态优先级问题,AI控制未开启"
                        else:
                            error_msg = control_result.get('msg', '未知错误') if control_result else '控制失败'
                            logger.warning(f"控制机器人 {robot_id} 的堰门失败: {error_msg}")
                            result['控制状态'] = f"控制堰门失败: {error_msg}"
                    except Exception as e:
                        logger.error(f"控制机器人 {robot_id} 的堰门时出错: {e}", exc_info=True)
                        result['控制状态'] = "控制堰门失败"
                else:
                    logger.info(f"检测到堰门需要控制，但不满足AI控制条件：ai_control_status={ai_control_status}, "
                               f"remote_control_status={remote_control_status}, "
                               f"on_site_control_status={on_site_control_status}, "
                               f"close_status={close_gate_status}")
                    result['控制状态'] = "不满足AI控制条件"
            else:
                # logger.info(f"检测到堰门需要控制，但距离上次控制仅过去 {time_elapsed:.1f} 分钟，需要等待至少{cooldown_minutes}分钟")
                # result['控制状态'] = f"冷却期未过，还需等待{cooldown_minutes-time_elapsed:.1f}分钟"
                logger.info(f"检测到堰门需要控制，但距离上次控制仅过去 {time_elapsed:.1f} 分钟")
                result['控制状态'] = "不需要控制"
        else:
            result['控制状态'] = "不需要控制堰门"
            
        # 在设置完控制状态后，发布结果到数据库
        if isinstance(result, dict):
            self.executor.submit(
                self._publish_result_to_database,
                camera_id,
                result,
                "排渣堰门识别",
                frame_path,
                robot_id
            )
            
        return result
    
    # --- 辅助方法 ---
    def _is_night_mode(self, log_message=True) -> bool:
        """检查当前是否处于夜间模式
        
        Args:
            log_message: 是否需要打印日志信息
            
        Returns:
            bool: 是否处于夜间模式
        """
        # 如果夜间模式未启用，直接返回False
        if not self.config_manager.night_mode_enabled:
            return False
            
        current_time = datetime.datetime.now()
        current_hour = current_time.hour
        night_start = self.config_manager.night_start_hour
        night_end = self.config_manager.night_end_hour
        
        # 判断当前是否在夜间时段
        # 如果night_start > night_end，表示跨越了午夜
        is_night = False
        if night_start > night_end:
            is_night = (current_hour >= night_start) or (current_hour < night_end)
        else:
            is_night = night_start <= current_hour < night_end
            
        # 控制日志打印频率
        if is_night and log_message:
            # 检查是否需要打印日志
            should_log = False
            
            # 如果是第一次检测到夜间模式，或者距离上次打印日志已经过去了至少1小时
            if self.last_night_mode_log_time is None:
                should_log = True
            else:
                time_since_last_log = (current_time - self.last_night_mode_log_time).total_seconds()
                # 根据配置的间隔打印日志
                if time_since_last_log >= self.config_manager.night_log_interval:
                    should_log = True
                    
            if should_log:
                logger.info(f"当前处于夜间模式 ({night_start}:00-{night_end}:00)，暂停所有识别和控制任务")
                self.last_night_mode_log_time = current_time
                
        return is_night
    
    def _preprocess_image_for_analysis(self, image_path: str, coords_file: str, task_name: str = "") -> str:
        """
        预处理图像用于分析。
        在调试模式下，会将处理结果保存到固定的调试路径。
        无论何种模式，都会返回一个用于分析的临时文件路径。
        
        Args:
            image_path: 原始图像路径
            coords_file: 坐标文件路径
            task_name: 任务名称，用于临时和调试文件命名

        Returns:
            str: 用于分析的临时文件路径，如果处理失败则返回原始图像路径
        """
        if not coords_file or not os.path.exists(coords_file):
            logger.info(f"未找到坐标文件 {coords_file}，将使用原始图像")
            return image_path
            
        try:
            from server.utils.image_extractor import extract_from_image
            import tempfile
            import uuid

            # 1. 统一提取图像数据
            logger.info(f"使用坐标文件 {coords_file} 处理图像 {image_path}")
            processed_image_np = extract_from_image(
                image_path=image_path,
                coordinates=coords_file,
                save=False,  # 始终不让它自己保存
                trim_border=True
            )

            # 2. 检查是否处于调试模式，并保存调试文件
            debug_mode = self.config_manager.robot_config.get('debug_mode', False)
            if debug_mode:
                # 创建调试目录
                debug_dir = Path(self.config_manager.base_dataset_path) / "debug_images"
                debug_dir.mkdir(parents=True, exist_ok=True)
                
                # 提取原始文件名作为基础
                original_filename = os.path.basename(image_path)
                base_name = os.path.splitext(original_filename)[0]
                
                # 创建带有时间戳的调试文件名
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                debug_filename = f"{base_name}_{task_name}_{timestamp}_debug.png"
                debug_filepath = str(debug_dir / debug_filename)
                
                # 保存调试图像
                cv2.imwrite(debug_filepath, processed_image_np)
                logger.info(f"调试模式：已保存处理后的图像到: {debug_filepath}")

            # 3. 始终创建并返回一个唯一的临时文件用于模型分析
            # 这样做可以保证并发安全，因为每个任务都在操作自己独立的临时文件
            temp_dir = tempfile.gettempdir()
            temp_filename = f"temp_{task_name}_{uuid.uuid4().hex}.png"
            temp_filepath = os.path.join(temp_dir, temp_filename)
            
            cv2.imwrite(temp_filepath, processed_image_np)
            logger.info(f"临时保存处理后的图像到: {temp_filepath}")
            
            return temp_filepath

        except Exception as e:
            logger.error(f"处理图像时出错: {e}，将使用原始图像", exc_info=True)
            return image_path

# ==============================================================================
# 6. 主程序入口
# ==============================================================================
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="机器人水池巡检系统测试工具")
    parser.add_argument("--mode", type=str, choices=["normal", "test_slag", "test_moss", "test_weir_gate", "test_braking"], 
                      default="normal", help="运行模式：normal=正常运行，test_slag=测试撇渣管识别，test_moss=测试青苔识别，test_weir_gate=测试堰门识别，test_braking=测试刹车状态")
    parser.add_argument("--image", type=str, help="用于测试的图像路径")
    parser.add_argument("--camera_id", type=str, default="test_camera", help="测试用摄像头ID")
    parser.add_argument("--robot_id", type=str, default="test_robot", help="测试用机器人ID")
    
    args = parser.parse_args()
    
    try:
        if args.mode == "normal":
            # 正常运行系统
            logger.info("正在初始化机器人巡检系统...")
            inspection_system = RobotPoolInspectionSystem.get_instance()
            inspection_system.start()
            
            # 使用事件来优雅地等待退出信号
            stop_event = threading.Event()
            def signal_handler(sig, frame):
                logger.info("接收到退出信号，正在停止系统...")
                stop_event.set()

            import signal
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            logger.info("系统已启动。按 Ctrl+C 退出。")
            stop_event.wait()
            
            if inspection_system.is_running():
                inspection_system.stop()
        else:
            # 测试模式需要图像路径
            if args.mode != "test_braking" and not args.image:
                logger.error("测试模式需要提供图像路径，请使用 --image 参数")
                sys.exit(1)
                
            if args.mode != "test_braking" and not os.path.exists(args.image):
                logger.error(f"图像文件不存在: {args.image}")
                sys.exit(1)
            
            # 初始化系统但不启动主循环
            inspection_system = RobotPoolInspectionSystem.get_instance()
            
            # 根据模式执行不同的测试
            if args.mode == "test_slag":
                logger.info(f"测试撇渣管识别，使用图像: {args.image}")
                result = inspection_system._execute_slag_outlet(args.image, args.camera_id, args.robot_id)
                logger.info(f"撇渣管识别结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
            elif args.mode == "test_moss":
                logger.info(f"测试青苔识别，使用图像: {args.image}")
                result = inspection_system._execute_moss_recognition(args.image, args.camera_id, args.robot_id)
                logger.info(f"青苔识别结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
            elif args.mode == "test_weir_gate":
                logger.info(f"测试堰门识别，使用图像: {args.image}")
                result = inspection_system._execute_slag_weir_gate(args.image, args.camera_id, args.robot_id)
                logger.info(f"堰门识别结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
            elif args.mode == "test_braking":
                logger.info(f"测试机器人刹车状态，机器人ID: {args.robot_id}")
                from server.remote.device import get_robot_braking_status
                result = get_robot_braking_status(args.robot_id)
                logger.info(f"机器人刹车状态: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
                # 测试带有刹车状态检查的青苔识别流程
                if args.image and os.path.exists(args.image):
                    logger.info(f"测试带有刹车状态检查的青苔识别，使用图像: {args.image}")
                    result = inspection_system._execute_moss_recognition(args.image, args.camera_id, args.robot_id)
                    logger.info(f"青苔识别结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
    
    except Exception as e:
        logger.critical(f"系统运行失败: {e}", exc_info=True)
        
    finally:
        if 'inspection_system' in locals() and inspection_system.is_running():
            inspection_system.stop()
        logger.info("系统已完全停止。")


# if __name__ == "__main__":

