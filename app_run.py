import time
import sys
import os
import logging
from datetime import datetime
from contextlib import asynccontextmanager
import uvicorn
from apscheduler.schedulers.background import BackgroundScheduler
from fastapi import FastAPI
from config_file import config  # 直接导入全局配置实例
from server.task import image_comparison # 导入 image_comparison 模块
from server.task import aerobic_tank_ops # 导入 aerobic_tank_ops 模块
from server.task import publish_foam_task # 导入泡沫数据发布模块
from server.task import robot_pool_inspection # 导入机器人圆形水池巡检模块
from server.task import generate_daily_reports_task # 导入设备日报生成任务模块
from server.task import generate_monthly_reports_task # 导入设备月报生成任务模块
from server.utils.logger import setup_logging

# 初始化日志系统
setup_logging()
logger = logging.getLogger(__name__)

app = FastAPI(title="AI视频处理API", description="用于处理视频流，识别不同的故障并进行报警")

prefix = '/ai'

def restart_program():
    """重启程序"""
    logger.info("准备重启服务器...")
    
    os.execv(sys.executable, ['python'] + sys.argv)

# FastAPI 启动事件，启动时运行一次任务
@app.on_event("startup")
def startup():
    from server.task.cam_task import start
    from server.task.opshub_cam_task import opshub_start
    logger.info("FastAPI 启动，开始执行定时任务")
    # 将时间戳转换为 datetime 对象
    run_date = datetime.fromtimestamp(time.time())  # 当前时间
    # 创建定时任务调度器
    scheduler = BackgroundScheduler()
    # 添加摄像头任务
    scheduler.add_job(start, 'date', run_date=run_date)
    logger.info("已添加摄像头任务")
    
    # 添加 opshub 摄像头任务
    # scheduler.add_job(opshub_start, 'date', run_date=run_date)
    # logger.info("已添加 opshub 摄像头任务")
    
    # 添加泡沫数据发布任务
    # scheduler.add_job(publish_foam_task.start, 'date', run_date=run_date)
    # logger.info("已添加泡沫数据发布任务")
    
    # 添加机器人圆形水池巡检系统
    # pool_inspection_system = robot_pool_inspection.RobotPoolInspectionSystem.get_instance()
    # pool_inspection_system.start()
    # logger.info("已添加机器人圆形水池巡检系统")
    
    # *************** 大坦沙独有任务:定时分析任务 *************** #
    # 添加每天上午10点执行 image_comparison 的任务
    scheduler.add_job(image_comparison.main, 'cron', hour=10, minute=0, id='image_comparison_morning')
    logger.info("已添加上午10点的图像对比任务")

    # 添加每天下午5点 (17点) 执行 image_comparison 的任务
    scheduler.add_job(image_comparison.main, 'cron', hour=17, minute=0, id='image_comparison_afternoon')
    logger.info("已添加下午5点的图像对比任务")
    
    # 添加每天早上9点执行 aerobic_tank_ops 的任务
    scheduler.add_job(aerobic_tank_ops.main, 'cron', hour=9, minute=0, id='aerobic_tank_ops_morning')
    logger.info("已添加上午9点的好氧池运行参数分析任务")
    
    # 添加每天晚上22点执行设备日报生成任务
    scheduler.add_job(generate_daily_reports_task.start_daily_reports_task, 'cron', hour=22, minute=0, id='daily_reports_task')
    logger.info("已添加晚上22点的设备日报生成任务")
    
    # 添加每月月底12点执行设备月报生成任务
    scheduler.add_job(generate_monthly_reports_task.start_monthly_reports_task, 'cron', day='last', hour=0, minute=0, id='monthly_reports_task')
    logger.info("已添加每月月底12点的设备月报生成任务")
    
    # 添加每天凌晨2点重启的任务
    # scheduler.add_job(restart_program, 'cron', hour=2)
    scheduler.start()

# FastAPI 关闭事件，关闭时停止任务
# @app.on_event("shutdown")
# def shutdown_event():
#     logger.info("FastAPI 关闭，停止所有任务")
#     # 停止机器人圆形水池巡检系统
#     pool_inspection_system = robot_pool_inspection.RobotPoolInspectionSystem.get_instance()
#     if pool_inspection_system.is_running():
#         pool_inspection_system.stop()
#         logger.info("已停止机器人圆形水池巡检系统")

if __name__ == '__main__':
    from server.cam import api
    app.include_router(api.router, prefix=prefix)
    
    # 直接使用全局配置实例
    # logger.debug(config.env)
    # logger.debug("----------------")
    # logger.debug(config.env_vars)
    logger.info("--------地址和端口信息--------")
    logger.info(f"主机: {config.host}")
    logger.info(f"端口: {config.port}")
    uvicorn.run(app, host=config.host, port=config.port)