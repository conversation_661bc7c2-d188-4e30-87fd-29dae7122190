"""
YOLO OBB (Oriented Bounding Box) 区域预测脚本
功能：使用训练好的 OBB 模型对图像特定区域进行旋转边界框检测
适用于：2560x1440分辨率图像，在左下角区域进行目标检测
作者：AI Assistant
日期：2025-07-17
"""

import cv2
import os
import sys
import numpy as np
import time
import datetime
import logging
from ultralytics import YOLO

# 设置环境变量，避免 GUI 相关错误
os.environ["QT_QPA_PLATFORM"] = "offscreen"
os.environ["OPENCV_IO_ENABLE_OPENEXR"] = "1"

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YoloOBBRegionDetector:
    def __init__(self, model_path, region_points=None, conf_threshold=0.25, iou_threshold=0.2, max_detections=10):
        """
        初始化 YOLO OBB 区域检测器
        
        参数:
            model_path: YOLO OBB 模型路径
            region_points: 检测区域字典，格式为 {"region-name": [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]}
            conf_threshold: 置信度阈值
            iou_threshold: NMS IoU 阈值
            max_detections: 每张图最大检测数量
        """
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.max_detections = max_detections
        
        # 默认区域设置 - 针对2560x1440图像的左下角区域
        if region_points is None:
            # 左下角统一区域 - 扩大上边界
            self.region_points = {
                "left-bottom-zone": [(0, 1440), (1024, 1440), (1024, 600), (0, 600)],   # 左下角整体区域 - 宽度1024px, 高度840px
            }
        else:
            self.region_points = region_points
            
        # 加载模型
        self.model = None
        self._load_model()
        
        # 区域计数器
        self.region_counters = {region_name: {} for region_name in self.region_points.keys()}
        
        # 获取类别名称
        self.class_names = self._get_class_names()
        
    def _get_class_names(self):
        """获取模型的类别名称"""
        try:
            if self.model and hasattr(self.model, 'names'):
                return self.model.names
            else:
                # 如果无法获取类别名称，返回默认映射
                return {0: 'object'}
        except Exception as e:
            logger.warning(f"无法获取类别名称: {e}")
            return {0: 'object'}
        
    def _load_model(self):
        """加载 YOLO OBB 模型"""
        try:
            self.model = YOLO(self.model_path)
            logger.info(f"已加载 OBB 模型: {self.model_path}")
            # 更新类别名称
            self.class_names = self._get_class_names()
            logger.info(f"模型类别: {self.class_names}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
            
    def _point_in_polygon(self, point, polygon):
        """
        检查点是否在多边形内（射线投射算法）
        
        参数:
            point: (x, y) 坐标点
            polygon: [(x1,y1), (x2,y2), ...] 多边形顶点列表
            
        返回:
            bool: 点是否在多边形内
        """
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
            
        return inside
        
    def _draw_regions(self, image):
        """
        在图像上绘制检测区域
        
        参数:
            image: 输入图像
            
        返回:
            绘制了区域的图像
        """
        overlay = image.copy()
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
        
        for i, (region_name, points) in enumerate(self.region_points.items()):
            color = colors[i % len(colors)]
            
            # 绘制多边形
            pts = np.array(points, np.int32)
            pts = pts.reshape((-1, 1, 2))
            cv2.polylines(overlay, [pts], True, color, 3)
            
            # 填充半透明区域
            cv2.fillPoly(overlay, [pts], color)
            
            # 添加区域标签
            label_pos = (points[0][0] + 10, points[0][1] - 10)
            cv2.putText(overlay, region_name, label_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
        
        # 混合原图和覆盖层
        alpha = 0.3
        result = cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0)
        return result
        
    def _filter_detections_by_region(self, detections, region_name):
        """
        根据区域过滤检测结果
        
        参数:
            detections: YOLO 检测结果
            region_name: 区域名称
            
        返回:
            在指定区域内的检测结果
        """
        if detections.obb is None or len(detections.obb) == 0:
            return []
            
        region_polygon = self.region_points[region_name]
        filtered_results = []
        
        # 获取检测框的中心点
        if hasattr(detections.obb, 'xywhr'):
            for i, xywhr in enumerate(detections.obb.xywhr):
                center_x, center_y = float(xywhr[0]), float(xywhr[1])
                
                # 检查中心点是否在区域内
                if self._point_in_polygon((center_x, center_y), region_polygon):
                    detection_info = {
                        'center': (center_x, center_y),
                        'bbox': xywhr.tolist(),
                        'conf': float(detections.obb.conf[i]),
                        'cls': int(detections.obb.cls[i]) if detections.obb.cls is not None else 0,
                        'region': region_name
                    }
                    filtered_results.append(detection_info)
                    
        return filtered_results
        
    def _update_region_counters(self, region_detections):
        """
        更新区域计数器
        
        参数:
            region_detections: 区域检测结果字典
        """
        for region_name, detections in region_detections.items():
            if region_name not in self.region_counters:
                self.region_counters[region_name] = {}
                
            for detection in detections:
                cls_id = detection['cls']
                if cls_id not in self.region_counters[region_name]:
                    self.region_counters[region_name][cls_id] = 0
                self.region_counters[region_name][cls_id] += 1
                
    def process_image(self, image_path, save_output=False, output_path=None, show_regions=True):
        """
        处理单个图像并进行区域检测
        
        参数:
            image_path: 图像文件路径
            save_output: 是否保存输出图像
            output_path: 输出图像保存路径
            show_regions: 是否显示检测区域
            
        返回:
            检测结果字典
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            logger.error(f"无法读取图像: {image_path}")
            return None
            
        logger.info(f"处理图像: {image_path}, 尺寸: {image.shape}")
        
        # 使用 YOLO OBB 模型进行检测
        results = self.model(
            image,
            conf=self.conf_threshold,
            iou=self.iou_threshold,
            max_det=self.max_detections,
            verbose=False
        )
        
        # 获取检测结果
        if len(results) > 0:
            detection_result = results[0]
        else:
            logger.warning("未获得检测结果")
            return None
            
        # 按区域过滤检测结果
        region_detections = {}
        total_detections = 0
        
        for region_name in self.region_points.keys():
            region_detections[region_name] = self._filter_detections_by_region(detection_result, region_name)
            total_detections += len(region_detections[region_name])
            
        logger.info(f"总检测数量: {total_detections}")
        for region_name, detections in region_detections.items():
            logger.info(f"区域 {region_name}: {len(detections)} 个检测")
            
        # 更新计数器
        self._update_region_counters(region_detections)
        
        # 绘制结果图像
        result_image = image.copy()
        
        # 绘制检测区域
        if show_regions:
            result_image = self._draw_regions(result_image)
            
        # 绘制检测结果
        result_image = self._draw_detections(result_image, region_detections)
        
        # 保存结果
        if save_output:
            if output_path is None:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = f"predict_result_obb_region/{base_name}_region_{timestamp}.jpg"
            else:
                # 检查 output_path 是目录还是文件路径
                if os.path.isdir(output_path) or (not os.path.exists(output_path) and not os.path.splitext(output_path)[1]):
                    # 如果是目录路径，生成完整的文件路径
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    base_name = os.path.splitext(os.path.basename(image_path))[0]
                    output_path = os.path.join(output_path, f"{base_name}_region_{timestamp}.jpg")
                
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            cv2.imwrite(output_path, result_image)
            logger.info(f"结果已保存: {output_path}")
            
        return {
            'image_path': image_path,
            'total_detections': total_detections,
            'region_detections': region_detections,
            'region_counters': self.region_counters.copy(),
            'result_image': result_image
        }
        
    def _draw_detections(self, image, region_detections):
        """
        在图像上绘制检测结果
        
        参数:
            image: 输入图像
            region_detections: 区域检测结果
            
        返回:
            绘制了检测结果的图像
        """
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]
        
        for i, (region_name, detections) in enumerate(region_detections.items()):
            color = colors[i % len(colors)]
            
            for detection in detections:
                center_x, center_y = detection['center']
                conf = detection['conf']
                cls_id = detection['cls']
                
                # 绘制中心点
                cv2.circle(image, (int(center_x), int(center_y)), 5, color, -1)
                
                # 绘制旋转边界框（如果有详细的角点信息）
                bbox = detection['bbox']  # [center_x, center_y, width, height, rotation]
                if len(bbox) >= 5:
                    # 计算旋转边界框的四个角点
                    cx, cy, w, h, angle = bbox[:5]
                    
                    # 计算角点
                    cos_a = np.cos(angle)
                    sin_a = np.sin(angle)
                    
                    corners = np.array([
                        [-w/2, -h/2],
                        [w/2, -h/2],
                        [w/2, h/2],
                        [-w/2, h/2]
                    ])
                    
                    # 旋转和平移
                    rotated_corners = np.array([
                        [cos_a * x - sin_a * y + cx, sin_a * x + cos_a * y + cy]
                        for x, y in corners
                    ], dtype=np.int32)
                    
                    # 绘制旋转边界框
                    cv2.polylines(image, [rotated_corners], True, color, 3)
                
                # 创建更清晰的标签显示
                class_name = self.class_names.get(cls_id, f'cls_{cls_id}')
                label = f"{class_name} {conf:.3f}"
                region_label = f"{region_name}"
                
                # 计算标签位置（在检测框上方）
                label_pos_y = int(center_y) - 25
                region_pos_y = int(center_y) - 45
                label_pos_x = int(center_x) - 20
                
                # 确保标签位置在图像范围内
                label_pos_y = max(20, label_pos_y)
                region_pos_y = max(40, region_pos_y)
                label_pos_x = max(10, label_pos_x)
                
                # 绘制标签背景（使标签更清晰）
                # 置信度标签背景
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
                cv2.rectangle(image, 
                             (label_pos_x - 5, label_pos_y - label_size[1] - 5),
                             (label_pos_x + label_size[0] + 5, label_pos_y + 5),
                             (0, 0, 0), -1)  # 黑色背景
                
                # 区域标签背景
                region_size = cv2.getTextSize(region_label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                cv2.rectangle(image,
                             (label_pos_x - 5, region_pos_y - region_size[1] - 5),
                             (label_pos_x + region_size[0] + 5, region_pos_y + 5),
                             (50, 50, 50), -1)  # 深灰色背景
                
                # 绘制标签文字
                cv2.putText(image, label, (label_pos_x, label_pos_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
                cv2.putText(image, region_label, (label_pos_x, region_pos_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
        return image
        
    def process_batch_images(self, image_folder, output_folder=None, image_extensions=None):
        """
        批量处理图像文件夹
        
        参数:
            image_folder: 图像文件夹路径
            output_folder: 输出文件夹路径
            image_extensions: 支持的图像格式
            
        返回:
            所有图像的检测结果列表
        """
        if image_extensions is None:
            image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp')
            
        if output_folder is None:
            output_folder = "predict_result_obb_region"
            
        # 确保输出目录存在
        os.makedirs(output_folder, exist_ok=True)
        
        # 获取所有图像文件
        image_files = [
            os.path.join(image_folder, f)
            for f in os.listdir(image_folder)
            if f.lower().endswith(image_extensions)
        ]
        
        logger.info(f"找到 {len(image_files)} 个图像文件")
        
        if len(image_files) == 0:
            logger.warning(f"在 {image_folder} 中未找到任何图像文件")
            return []
            
        # 重置计数器
        self.reset_counters()
        
        all_results = []
        successful_count = 0
        
        for i, image_path in enumerate(image_files):
            try:
                logger.info(f"处理图像 {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
                
                # 设置输出路径
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = os.path.join(output_folder, f"{base_name}_region.jpg")
                
                # 处理图像
                result = self.process_image(image_path, save_output=True, output_path=output_path)
                
                if result is not None:
                    all_results.append(result)
                    successful_count += 1
                    
            except Exception as e:
                logger.error(f"处理图像 {image_path} 时出错: {e}")
                
        # 输出统计信息
        logger.info("="*60)
        logger.info("批量处理完成！统计信息如下：")
        logger.info("="*60)
        logger.info(f"总处理图像: {len(image_files)} 张")
        logger.info(f"成功处理: {successful_count} 张")
        logger.info(f"失败数量: {len(image_files) - successful_count} 张")
        logger.info(f"结果保存目录: {output_folder}")
        
        # 输出最终计数统计
        logger.info("\n最终区域计数统计:")
        for region_name, counters in self.region_counters.items():
            total_in_region = sum(counters.values())
            logger.info(f"  {region_name}: {total_in_region} 个检测 {counters}")
            
        return all_results
        
    def reset_counters(self):
        """重置所有区域计数器"""
        self.region_counters = {region_name: {} for region_name in self.region_points.keys()}
        logger.info("所有区域计数器已重置")
        
    def get_counter_stats(self):
        """获取当前计数统计"""
        return self.region_counters.copy()


# 使用示例
if __name__ == "__main__":
    # 模型路径
    model_path = r"F:\工作\E9\大坦沙\2025-07-24\opshub-ai\llms\models\yolo\best-piezha.pt"
    
    # 自定义区域设置 - 针对2560x1440图像的左下角
    # 区域坐标说明：[(左下角), (右下角), (右上角), (左上角)]
    # 坐标格式：(x, y) - x从左到右(0-2560), y从上到下(0-1440)
    custom_regions = {
        "left-bottom-zone": [(0, 1440), (1024, 1440), (1024, 900), (0, 900)],    # 左下角整体区域 - 宽度1024px, 高度840px
    }
    
    # 创建检测器实例
    detector = YoloOBBRegionDetector(
        model_path=model_path,
        region_points=custom_regions,
        conf_threshold=0.25,
        iou_threshold=0.2,
        max_detections=10
    )
    
    # ===================== 单张图像测试 =====================
    logger.info("开始单张图像测试...")
    test_image = r"F:\工作\E9\大坦沙\2025-07-24\opshub-ai\assets\robot_pool_4052_20250725_120408_region_20250725_120414.jpg"
    
    if os.path.exists(test_image):
        result = detector.process_image(
            test_image,
            save_output=True,
            output_path="tests/predict_result_obb_region/test_single_region.jpg"
        )
        
        if result:
            logger.info("单张图像检测完成")
            logger.info(f"总检测数量: {result['total_detections']}")
            for region_name, detections in result['region_detections'].items():
                logger.info(f"区域 {region_name}: {len(detections)} 个检测")
    else:
        logger.warning(f"测试图像不存在: {test_image}")
    
    # ===================== 批量图像测试 =====================
    # logger.info("\n开始批量图像测试...")
    # image_folder = "/home/<USER>/llm_project/yolo_project/datasets/大坦沙/project-12-at-2025-07-17-08-15-38b53558/val/images"
    
    # if os.path.exists(image_folder):
    #     batch_results = detector.process_batch_images(
    #         image_folder=image_folder,
    #         output_folder="predict_result_obb_region"
    #     )
        
    #     logger.info(f"批量处理完成，共处理 {len(batch_results)} 张图像")
    # else:
    #     logger.warning(f"图像文件夹不存在: {image_folder}")
    
    # logger.info("所有测试完成！")
