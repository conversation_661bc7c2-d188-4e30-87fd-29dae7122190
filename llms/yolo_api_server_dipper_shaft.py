# 使用yolo进行OBB(Oriented Bounding Box)目标检测
from ultralytics import YOLO
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import cv2
import os
import sys
import time
import logging
# from llms.utils.logger import setup_logging
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 初始化日志
# setup_logging()
logger = logging.getLogger(__name__)

class DipperShaftDetector:
    def __init__(self, model_path: str = "llms/models/yolo/best-dipper-shaft.pt"):
        """初始化耙斗井检测器(OBB模型)
        
        Args:
            model_path: YOLO OBB模型路径
        """
        self.model = YOLO(model_path, task="obb")  # 指定为OBB任务
        logger.info(f"耙斗井OBB检测器初始化完成，使用模型: {model_path}")
        
    def detect_devices(self, image: Union[np.ndarray, str], save_result: bool = False) -> Dict[str, List[Dict]]:
        """检测图像中的耙斗井设备(OBB检测)
        
        Args:
            image: 输入图像数组(np.ndarray)或图片路径(str)
            save_result: 是否保存识别结果图像到tests文件夹
            
        Returns:
            Dict包含:
            - devices: 检测到的设备列表
              每个设备包含:
              - id: 设备序号(从0开始)
              - status: 状态(类别名称)
              - confidence: 检测置信度
              - obb: OBB边界框信息
                - xyxyxyxy: 四个角点坐标
                - xywhr: 中心坐标+宽高+旋转角度
              - bbox: 水平边界框坐标[x1,y1,x2,y2](用于兼容)
            - image_path: 如果save_result=True，则包含保存的结果图像路径
        """
        # 如果输入是图片路径，则使用cv2读取图片
        input_path = None
        if isinstance(image, str):
            if not os.path.exists(image):
                raise FileNotFoundError(f"图片路径不存在: {image}")
            input_path = image
            image = cv2.imread(image)
            if image is None:
                raise ValueError(f"无法读取图片: {input_path}")
        
        # 确保image是np.ndarray类型
        if not isinstance(image, np.ndarray):
            raise TypeError(f"image必须是np.ndarray或str类型，当前类型: {type(image)}")
            
        # 直接使用原始图像
        processed_image = image
        
        # 使用YOLO OBB模型进行检测
        results = self.model(processed_image, conf=0.25, iou=0.2, max_det=5, imgsz=640, device='cpu')  # 使用cpu推理
        
        # 解析OBB结果
        devices = []
        if len(results) > 0:
            result = results[0]  # 获取第一张图片的结果
            
            # 处理OBB检测框
            if result.obb is not None and len(result.obb) > 0:
                for i in range(len(result.obb)):
                    obb = result.obb[i]
                    cls_name = result.names[int(obb.cls[0])]  # 获取类别名称
                    conf = float(obb.conf[0])  # 置信度
                    
                    # 获取OBB信息
                    obb_info = {}
                    if hasattr(obb, 'xyxyxyxy'):
                        obb_info['xyxyxyxy'] = obb.xyxyxyxy[0].cpu().numpy().tolist()  # 四个角点
                    if hasattr(obb, 'xywhr'):
                        obb_info['xywhr'] = obb.xywhr[0].cpu().numpy().tolist()  # 中心+宽高+角度
                    
                    # 计算水平边界框(用于兼容性)
                    if 'xyxyxyxy' in obb_info:
                        points = np.array(obb_info['xyxyxyxy']).reshape(-1, 2)
                        x_min, y_min = np.min(points, axis=0)
                        x_max, y_max = np.max(points, axis=0)
                        bbox = [float(x_min), float(y_min), float(x_max), float(y_max)]
                    else:
                        bbox = [0, 0, 0, 0]  # 默认值
                    
                    devices.append({
                        "id": i,
                        "status": cls_name,
                        "confidence": conf,
                        "obb": obb_info,
                        "bbox": bbox  # 兼容性字段
                    })
        
        response = {"devices": devices}
        
        # 保存结果图像
        if save_result:
            # 确保tests文件夹存在
            tests_dir = "tests"
            os.makedirs(tests_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            if input_path:
                base_name = os.path.basename(input_path)
                filename = f"{timestamp}_{base_name}"
            else:
                filename = f"{timestamp}_result.jpg"
            
            save_path = os.path.join(tests_dir, filename)
            
            # 绘制并保存结果图像
            annotated_img = results[0].plot()
            
            # 在图像上标注设备ID
            for device in devices:
                bbox = device["bbox"]
                device_id = device["id"]
                x, y = int(bbox[0]), int(bbox[1]) - 10  # 在边界框左上角上方显示设备ID
                cv2.putText(annotated_img, f"ID: {device_id}", (x, y), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            
            cv2.imwrite(save_path, annotated_img)
            response["image_path"] = save_path
            
        return response

# 创建检测器实例
detector = DipperShaftDetector()

# 测试代码
if __name__ == "__main__":
    # 测试图片路径参数
    test_image_path = r"F:\工作\E9\广州竹料污水厂\2025-0711/eadb8837-frame_4056_2025_01_15_11_19_35.png"  # 修改为实际存在的测试图片
    if os.path.exists(test_image_path):
        results_path = detector.detect_devices(test_image_path, save_result=True)
        logger.info("OBB检测结果: %s", results_path)
    else:
        logger.warning(f"测试图片不存在: {test_image_path}")
        print("请提供有效的测试图片路径")
