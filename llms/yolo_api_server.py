# 使用yolo进行目标检测

from ultralytics import YOLO
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import cv2
import os
import sys
import time
import logging
# from llms.utils.logger import setup_logging
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llms.utils.transform_processor import process_image

# 初始化日志
# setup_logging()
logger = logging.getLogger(__name__)

class DeviceDetector:
    def __init__(self, model_path: str = "llms/models/yolo/best-dipper.pt"):
        """初始化设备检测器
        
        Args:
            model_path: YOLO模型路径
        """
        self.model = YOLO(model_path, task="detect")
        
    def detect_devices(self, image: Union[np.ndarray, str], save_result: bool = False) -> Dict[str, List[Dict]]:
        """检测图像中的设备状态
        
        Args:
            image: 输入图像数组(np.ndarray)或图片路径(str)
            save_result: 是否保存识别结果图像到tests文件夹
            
        Returns:
            Dict包含:
            - devices: 检测到的设备列表
              每个设备包含:
              - device_id: 设备编号(1-3,从左到右)
              - status: 状态("incline"或"no_incline")
              - confidence: 检测置信度
              - bbox: 边界框坐标[x1,y1,x2,y2]
            - image_path: 如果save_result=True，则包含保存的结果图像路径
        """
        # 如果输入是图片路径，则使用cv2读取图片
        input_path = None
        if isinstance(image, str):
            if not os.path.exists(image):
                raise FileNotFoundError(f"图片路径不存在: {image}")
            input_path = image
            image = cv2.imread(image)
            if image is None:
                raise ValueError(f"无法读取图片: {input_path}")
        
        # 确保image是np.ndarray类型
        if not isinstance(image, np.ndarray):
            raise TypeError(f"image必须是np.ndarray或str类型，当前类型: {type(image)}")
            
        # 无论输入是路径还是numpy数组，都使用process_image处理
        processed_image = process_image(image, params_file='llms/utils/split_coords/pre_line.txt')
        if processed_image is None:
            raise ValueError(f"图像处理失败")
            
        # 获取图像宽度
        img_width = processed_image.shape[1]
        
        # 定义三个设备位置的区域范围(按图像宽度的比例划分)
        position_ranges = [
            (0, img_width / 3),               # 位置1 (左)
            (img_width / 3, 2 * img_width / 3), # 位置2 (中)
            (2 * img_width / 3, img_width)      # 位置3 (右)
        ]
        logger.debug('位置范围: %s', str(position_ranges)) 
        results = self.model(processed_image, conf=0.5, iou=0.4, max_det=3, imgsz=640, device='cpu')  # 使用cpu推理
        
        # 解析结果
        devices = []
        if len(results) > 0:
            result = results[0]  # 获取第一张图片的结果
            boxes = result.boxes
            
            # 处理检测框
            if len(boxes) > 0:
                for i in range(len(boxes)):
                    box = boxes[i]
                    bbox = box.xyxy[0].cpu().numpy().tolist()  # 边界框坐标
                    
                    # 计算边界框中心点x坐标
                    box_center_x = (bbox[0] + bbox[2]) / 2
                    
                    # 根据中心点确定设备位置(1,2,3)
                    device_id = None
                    for pos_id, (start, end) in enumerate(position_ranges):
                        if start <= box_center_x < end:
                            device_id = pos_id + 1  # 设备ID为位置索引+1
                            break
                    
                    # 如果无法确定位置(极少情况)，则使用索引+1作为设备ID
                    if device_id is None:
                        device_id = i + 1
                    
                    cls_name = result.names[int(box.cls[0])]  # 获取类别名称
                    conf = float(box.conf[0])  # 置信度
                    
                    devices.append({
                        "device_id": device_id,
                        "status": cls_name,
                        "confidence": conf,
                        "bbox": bbox,
                        "position": f"位置{device_id}" # 添加位置描述便于理解
                    })
                
                # 按device_id排序
                devices.sort(key=lambda x: x["device_id"])
        
        response = {"devices": devices}
        
        # 保存结果图像
        if save_result:
            # 确保tests文件夹存在
            tests_dir = "tests"
            os.makedirs(tests_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            if input_path:
                base_name = os.path.basename(input_path)
                filename = f"{timestamp}_{base_name}"
            else:
                filename = f"{timestamp}_result.jpg"
            
            save_path = os.path.join(tests_dir, filename)
            
            # 绘制并保存结果图像
            annotated_img = results[0].plot()
            
            # 在图像上标注设备ID
            for device in devices:
                bbox = device["bbox"]
                device_id = device["device_id"]
                x, y = int(bbox[0]), int(bbox[1]) - 10  # 在边界框左上角上方显示设备ID
                cv2.putText(annotated_img, f"ID: {device_id}", (x, y), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.9, (0, 255, 0), 2)
            
            cv2.imwrite(save_path, annotated_img)
            response["image_path"] = save_path
            
        return response

# 创建检测器实例
detector = DeviceDetector()

# 测试代码
if __name__ == "__main__":
    # import cv2
    # # 测试图像
    # image = cv2.imread("assets/2.jpg")
    # results = detector.detect_devices(image)
    # logger.info("检测结果: %s", results)
    
    # 测试图片路径参数
    results_path = detector.detect_devices("assets/08.png", save_result=True)
    logger.info("路径参数检测结果: %s", results_path)